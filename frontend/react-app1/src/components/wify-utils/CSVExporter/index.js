import {
    ClockCircleFilled,
    ClockCircleOutlined,
    FileExcelFilled,
    FileExcelOutlined,
    SendOutlined,
} from '@ant-design/icons';
import {
    <PERSON>ton,
    Card,
    Col,
    Collapse,
    Form,
    Input,
    message,
    Row,
    Spin,
    Checkbox,
    Alert,
} from 'antd';
import React, { Component } from 'react';
import { defaultStyles, FileIcon } from 'react-file-icon';
import FormBuilder from 'antd-form-builder';
import { NoData, priorities } from '../../../util/helpers';
import RemoteSourceSelect from '../../../components/wify-utils/RemoteSourceSelect';
import UserSelectorWidget from '../../../components/wify-utils/UserSelectorWidget';
import { getAddressFieldsMeta } from '../../../util/CustomerHelpers';
import CircularProgress from '../../../components/CircularProgress';
import http_utils from '../../../util/http_utils';
import './style.css';

const inputProto = {
    email_id: '<EMAIL>',
    subject: 'Urban Ladder TMS Installation dump 01/09/2021 05:37:00pm',
    filters: {
        creation_date: '2021-08-09',
    },
    section_wise_meta: [
        {
            key: 'request_info_fields', //this should be used for select all checkbox
            label: 'Request Info',
            fields_meta: [
                {
                    key: 'request_description',
                    colSpan: 4,
                    label: 'Description',
                    widget: 'textarea',
                    required: true,
                },
                {
                    key: 'request_req_date',
                    label: 'Req. Service Date',
                    colSpan: 2,
                    widgetProps: {
                        style: {
                            width: '100%',
                        },
                    },
                    widget: 'date-picker',
                },

                {
                    key: 'request_priority',
                    label: 'Priority',
                    colSpan: 2,
                    widget: 'select',
                    options: priorities, // to be loaded from API
                    required: true,
                    widgetProps: {
                        onChange: (value) => {
                            // console.log('Priority - ',value);
                        },
                    },
                },
                {
                    key: 'request_cc_users',
                    label: 'CC users',
                    widget: UserSelectorWidget,
                    widgetProps: {
                        mode: 'multiple',
                        // onChange : value => {
                        //     console.log('CC users value - ',value);
                        // }
                    },
                    renderView: (values) => (
                        <div>
                            {typeof values.map == 'function' &&
                                values.map((value) => value.label + ',')}
                        </div>
                    ),
                    colSpan: 2,
                },
            ],
        },
    ],
};

class CSVExporter extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
    }

    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
        formMeta: undefined,
        section_vs_status: {},
        showColumnInfo: this.props.showColumnInfo || false,
    };

    state = this.initState;

    componentDidMount() {
        this.syncDataProto();
    }

    onSectionCheckAllChange(section_key, checked) {
        let fieldsToSet = {};
        if (checked) {
            // if checked
            let selected_section = this.state.formMeta.columns_sections.filter(
                (column_section) => column_section.key == section_key
            );
            // message.success(JSON.stringify(selected_section));
            if (selected_section.length > 0) {
                selected_section = selected_section[0];

                fieldsToSet[section_key] = selected_section.options.map(
                    (single_option) => single_option.value
                );
            }
        } else {
            // if unchecked
            fieldsToSet[section_key] = [];
        }
        this.formRef.current.setFieldsValue(fieldsToSet);
        let section_vs_status = this.state.section_vs_status;
        if (section_vs_status[section_key]) {
            section_vs_status[section_key]['checked'] = checked;
            if (checked) {
                section_vs_status[section_key]['indeterminate'] = false;
            }
            this.setState({
                section_vs_status: section_vs_status,
            });
        }
    }

    setCheckAllBoxFrSection(section_key) {
        let selected_section = this.state.formMeta.columns_sections.filter(
            (column_section) => column_section.key == section_key
        );
        // message.success(JSON.stringify(selected_section));
        if (selected_section.length > 0) {
            selected_section = selected_section[0];
            let selected_options_count =
                this.formRef?.current.getFieldValue(section_key).length;
            let total_options_count = selected_section.options.length;
            let section_vs_status = this.state.section_vs_status;
            let newAttributesFrSectionCheckbox = section_vs_status[section_key];
            if (selected_options_count == 0) {
                // nothing selected
                newAttributesFrSectionCheckbox = {
                    checked: false,
                    indeterminate: false,
                };
            } else if (total_options_count == selected_options_count) {
                // all selected
                // indeterminate
                newAttributesFrSectionCheckbox = {
                    checked: true,
                    indeterminate: false,
                };
            } else if (total_options_count > selected_options_count) {
                // some are selected
                newAttributesFrSectionCheckbox = {
                    checked: false,
                    indeterminate: true,
                };
            }
            section_vs_status[section_key] = newAttributesFrSectionCheckbox;
            this.setState({
                section_vs_status: section_vs_status,
            });
        }
    }

    syncDataProto() {
        let exportMeta = { ...this.props.exportMeta };
        if (this.props.demoMode) {
            exportMeta = inputProto;
        }
        let columns_sections = [];

        exportMeta.section_wise_meta &&
            exportMeta.section_wise_meta.map((singleSectionWithMeta) => {
                let singleSectionFrMeta = { ...singleSectionWithMeta };
                delete singleSectionFrMeta['fields_meta'];
                delete singleSectionFrMeta['label'];
                singleSectionFrMeta['widget'] = 'checkbox-group';
                singleSectionFrMeta['options'] = [];
                singleSectionFrMeta['widgetProps'] = {
                    onChange: (value) => {
                        this.setCheckAllBoxFrSection(singleSectionFrMeta.key);
                    },
                };
                singleSectionWithMeta.fields_meta.map((singleFieldMeta) => {
                    if (!singleFieldMeta.render) {
                        singleSectionFrMeta.options.push({
                            value: singleFieldMeta.key,
                            label: singleFieldMeta.label,
                        });
                    }
                });
                columns_sections.push(singleSectionFrMeta);
            });
        exportMeta['columns_sections'] = columns_sections;
        if (columns_sections.length == 0 && !this.props?.noColumnSelection) {
            exportMeta = undefined;
        }
        this.setState({
            formMeta: exportMeta,
        });
    }

    getFormMetaFrEmailEntities() {
        const meta = {
            formItemLayout: null,
            fields: [
                {
                    key: 'email_id',
                    label: 'Email',
                    extra: (
                        <small>
                            (Selected data will be emailed as an attachment to
                            mentioned address)
                        </small>
                    ),
                    required: true,
                    rules: [
                        {
                            type: 'email',
                            max: 100,
                        },
                    ],
                },
                {
                    key: 'subject',
                    label: 'Subject',
                    required: true,
                },
            ],
        };
        return meta;
    }
    getSaveSelectionFormMeta() {
        const meta = {
            fields: [
                {
                    key: 'save_selection',
                    extra: 'Save column selection for future use',
                    widget: 'switch',
                    initialValue: true,
                },
            ],
        };
        return meta;
    }

    getAllFieldsKeyVsLabel() {
        let columns_sections = this.state.formMeta.columns_sections;
        let finalMapping = {};
        columns_sections.map((singleSection) => {
            singleSection.options.map((singleField) => {
                finalMapping[singleField.value] = singleField.label;
            });
        });
        return finalMapping;
    }

    getSelectedColumns(full_form_data) {
        let columns_sections = this.state.formMeta.columns_sections;
        let finalArray = [];
        columns_sections.map((singleSection) => {
            if (full_form_data[singleSection.key]) {
                singleSection.options.forEach((singleOption) => {
                    if (
                        full_form_data[singleSection.key].includes(
                            singleOption.value
                        )
                    ) {
                        finalArray.push(singleOption.value);
                    }
                });
            }
        });
        return finalArray;
    }

    submitForm = (data) => {
        this.setState({
            isFormSubmitting: true,
        });
        var params = data;
        params['field_label_mapping'] = this.getAllFieldsKeyVsLabel();
        params['selected_columns'] = this.getSelectedColumns(params);
        params['filters'] = JSON.stringify(this.props.exportMeta.filters);
        // console.log("params",params);

        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: false,
            });
            if (this.props.onSubmitComplete) {
                this.props.onSubmitComplete(resp);
            }
            // this.tellParentToRefreshList(resp.entry_id);
            // this.updateClosureToParent();
        };
        const onError = (error) => {
            console.log('error', error);
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };

        http_utils.performPostCall(
            this.props.submitUrl,
            params,
            onComplete,
            onError
        );
    };

    getFormMetaFrSingleSection(fieldsMetaFrSection) {
        let meta = {
            fields: [fieldsMetaFrSection],
        };
        return meta;
    }

    render() {
        const { formMeta, isFormSubmitting, error, section_vs_status } =
            this.state;
        let prefillFormData = undefined;
        if (formMeta) {
            prefillFormData = { ...formMeta };
            delete prefillFormData['section_wise_meta'];
        }
        return formMeta ? (
            <div>
                <div className="gx-d-flex">
                    <h3>
                        <i className="icon icon-email gx-vertical-align-middle"></i>{' '}
                        EMAIL CSV report
                    </h3>
                </div>
                <hr className="gx-mt-0 gx-bg-dark"></hr>
                <Form
                    // {...formItemLayout}
                    className="gx-w-100"
                    layout="vertical"
                    initialValues={prefillFormData || {}}
                    ref={this.formRef}
                    onFinish={(data) => {
                        // message.error(JSON.stringify(data));
                        this.submitForm(data);
                    }}
                >
                    <FormBuilder
                        form={this.formRef}
                        meta={this.getFormMetaFrEmailEntities()}
                    />
                    {!this.props?.isFiltersAppliedHide && (
                        <p className="gx-text-green gx-mt-3">
                            Filters applied - (
                            {Object.keys(formMeta.filters).length})
                        </p>
                    )}
                    {formMeta.columns_sections.length > 0 && (
                        <>
                            <p className="gx-mt-3">Select columns</p>
                            <Collapse>
                                {formMeta.columns_sections.map(
                                    (singleSection, sectionNo) => (
                                        <Collapse.Panel
                                            key={sectionNo}
                                            header={
                                                <span>
                                                    <Checkbox
                                                        {...(section_vs_status[
                                                            singleSection.key
                                                        ] || {})}
                                                        onChange={(e) =>
                                                            this.onSectionCheckAllChange(
                                                                singleSection.key,
                                                                e.target.checked
                                                            )
                                                        }
                                                    />
                                                    <span className="gx-ml-2">
                                                        {singleSection.text}
                                                    </span>
                                                </span>
                                            }
                                        >
                                            <FormBuilder
                                                form={this.formRef}
                                                meta={this.getFormMetaFrSingleSection(
                                                    singleSection
                                                )}
                                            />
                                        </Collapse.Panel>
                                    )
                                )}
                            </Collapse>
                        </>
                    )}

                    {/* <div className="gx-mt-3">
                        <FormBuilder 
                            form={this.formRef}
                            meta={this.getSaveSelectionFormMeta()} />
                    </div> */}
                    {this.state.showColumnInfo && (
                        <Alert
                            type="info"
                            showIcon
                            className="gx-mt-3"
                            message="The following columns will appear in the exported CSV:"
                            description={
                                <ul>
                                    {formMeta.column_names.map((field) => (
                                        <li key={field.key}>{field.label}</li>
                                    ))}
                                </ul>
                            }
                        />
                    )}

                    <Form.Item className="gx-mt-3">
                        <Button
                            type="primary"
                            htmlType="submit"
                            disabled={isFormSubmitting}
                        >
                            <SendOutlined /> Send report
                        </Button>
                        {isFormSubmitting ? <Spin /> : null}
                    </Form.Item>
                    {error ? <p className="gx-text-red">{error}</p> : null}
                </Form>
            </div>
        ) : (
            <NoData />
        );
    }
}

export default CSVExporter;
