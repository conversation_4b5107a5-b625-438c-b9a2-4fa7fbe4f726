import React, { useEffect, useState } from 'react';
import { Menu, Tag, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import { FaUserAstronaut } from 'react-icons/fa';
import { LuUsers2 } from 'react-icons/lu';
import { TbTransform, TbTransferIn } from 'react-icons/tb';
import { MobileOutlined } from '@ant-design/icons';
import { RiUserSettingsLine } from 'react-icons/ri';
import CaptureLoginAttendance from '../../components/WIFY/CaptureLoginAttendance';
import ConfigHelpers from '../../util/ConfigHelpers';
import IntlMessages from '../../util/IntlMessages';
import { useSelector } from 'react-redux';
import {
    NAV_STYLE_NO_HEADER_EXPANDED_SIDEBAR,
    NAV_STYLE_NO_HEADER_MINI_SIDEBAR,
    THEME_TYPE_LITE,
} from '../../constants/ThemeSetting';
import { getCurrentDay, getLinktoObject } from '../../util/helpers';
import checkFeatureAccess from '../../util/FeatureAccess';

const SubMenu = Menu.SubMenu;
const MenuItemGroup = Menu.ItemGroup;
const userViewMenuKeys = {
    main: 'main',
    dashboardUser: 'dashboard-user',
    dashboardPrvdr: 'dashboard-prvdr',
    dashboardOnfield: 'dashboard-onfield',
    myAvailability: 'my-availability',
    exceptions: 'exceptions',
    runs: 'runs',
    autoAssign: 'auto-assign',
    quickAssignment: 'quick-assignment',
    capacityDashboard: 'capacity-dashboard',
    dashboardOwner: 'dashboard-owner',
    ratings: 'ratings',
    taskWise: 'task-wise',
    dayWise: 'day-wise',
    manageStock: 'manage-stock',
    owSetup: 'ow-setup',
    configuration: 'configuration',
    createServiceTypes: 'create-service-types',
    manageServiceTypes: 'manage-service-types',
    manageSubtaskTypes: 'manage-subtask-types',
    manageFeatureFlags: 'manage-feature-flags',
    users: 'users',
    organisations: 'organisations',
    apiDocs: 'api-docs',
    insights: 'insights',
    serviceMetrics: 'serviceMetrics',
    visitMap: 'visitMap',
    siteMap: 'siteMap',
    taskUpdates: 'taskUpdates',
    projects: 'projects',
    jobBroadcasts: 'job-broadcasts',
    myTasks: 'my-tasks',
    myLeaves: 'my-leaves',
    deploymentRequests: 'deployment-requests',
    myInventory: 'my-inventory',
    attendance: 'attendance',
    inventory: 'inventory',
    leaves: 'leaves',
    assignment: 'assignment',
    siteAttendance: 'attendance',
    officeAttendance: 'office_attendance',
    dailyReport: 'daily_report',
    rangeReport: 'range-report',
    manageInventory: 'manage-inventory',
    manageSkus: 'manage-skus',
    manageSpares: 'manage-spares',
    manageWarehouse: 'manage-warehouse',
    stockTransfer: 'stock-transfer',
    interWarehouse: 'inter-warehouse',
    toSp: 'to-sp',
    transferhistory: 'transfer-history',
    incomingTransfer: 'incoming-transfer',
    toTechnician: 'to-technician',
    spInventoryMaster: 'sp-inventory-master',
    customerAccess: 'customer-access',
    customerRequests: 'customer-requests',
    serviceRequests: 'service-requests',
    services: 'services',
    more: 'more',
    customer: 'customer',
    technicianAppAccess: 'technician-app-access',
    myAccount: 'my-account',
    profileDetails: 'profile-details',
    mySettings: 'my-settings',
    changeLog: 'change-log',
    toggleRole: 'toggle-role',
    availabilityReport: 'availability-report',
};

const UserViewMenu = () => {
    const [featureAccess, setFeatureAccess] = useState({});
    let { navStyle, themeType } = useSelector(({ settings }) => settings);
    let { pathname } = useSelector(({ common }) => common);

    useEffect(() => {
        const verifyFeatureAccess = async () => {
            try {
                let hasAccess = await checkFeatureAccess('TMS250303485281');
                let hasAccessTMS250514964399 =
                    await checkFeatureAccess('TMS250514964399');
                let hasAccessTMS250613586199 =
                    await checkFeatureAccess('TMS250613586199');
                setFeatureAccess({
                    TMS250303485281: hasAccess,
                    TMS250514964399: hasAccessTMS250514964399,
                    TMS250613586199: hasAccessTMS250613586199,
                });
            } catch (error) {
                setFeatureAccess({
                    TMS250303485281: false,
                    TMS250514964399: false,
                    TMS250613586199: false,
                });
                console.error(
                    'UserViewMenu :: verifyFeatureAccess :: error : ',
                    error
                );
            }
        };
        verifyFeatureAccess();
    }, []);

    const { access_service_routes, config_data, roles, access_static_routes } =
        useSelector(({ auth }) => auth.authUser);

    const getNoHeaderClass = (navStyle) => {
        if (
            navStyle === NAV_STYLE_NO_HEADER_MINI_SIDEBAR ||
            navStyle === NAV_STYLE_NO_HEADER_EXPANDED_SIDEBAR
        ) {
            return 'gx-no-header-notifications';
        }
        return '';
    };
    const getNavStyleSubMenuClass = (navStyle) => {
        if (navStyle === NAV_STYLE_NO_HEADER_MINI_SIDEBAR) {
            return 'gx-no-header-submenu-popup';
        }
        return '';
    };
    let selectedKeys = pathname.substr(1);
    const defaultOpenKeys = pathname.substr(1).split('/')[1];
    // Extract the path segments from the selectedKeys
    const pathSegment = selectedKeys.split('/');

    if (!pathSegment.includes(userViewMenuKeys.services)) {
        for (let i = pathSegment.length - 1; i >= 0; i--) {
            if (Object.values(userViewMenuKeys).includes(pathSegment[i])) {
                selectedKeys = pathSegment[i];
                break;
            }
        }
    }

    return (
        <Menu
            defaultOpenKeys={[defaultOpenKeys]}
            selectedKeys={[selectedKeys]}
            theme={themeType === THEME_TYPE_LITE ? 'lite' : 'dark'}
            mode="inline"
            className="gx-mb-5"
        >
            <div className="gx-mx-3 gx-mt-3 gx-d-lg-none">
                {!ConfigHelpers.isOwner() && <CaptureLoginAttendance />}
            </div>
            <MenuItemGroup
                key={userViewMenuKeys.main}
                className="gx-menu-group"
                title={<IntlMessages id="sidebar.main" />}
            ></MenuItemGroup>

            {featureAccess?.TMS250303485281 &&
                !ConfigHelpers.isOwner() &&
                ConfigHelpers.isUserOnfield() &&
                ConfigHelpers.orgHasSbtskType() && (
                    <Menu.Item key={userViewMenuKeys.dashboardOnfield}>
                        <Link
                            to={getLinktoObject('/dashboard-onfield', {
                                filters: {
                                    sbtsk_due_day: ['today'],
                                },
                            })}
                        >
                            <i className="icon icon-ckeditor" />
                            <span>Home</span>
                        </Link>
                    </Menu.Item>
                )}
            {featureAccess?.TMS250303485281 &&
                !ConfigHelpers.isOwner() &&
                ConfigHelpers.isUserOnfield() && (
                    <Menu.Item key={userViewMenuKeys.myAvailability}>
                        <Link
                            to={getLinktoObject(
                                `/${userViewMenuKeys.myAvailability}`
                            )}
                        >
                            <i className="icon icon-calendar-new" />
                            <span>My availability</span>
                        </Link>
                    </Menu.Item>
                )}

            {!ConfigHelpers.isServiceProvider() && !ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.dashboardUser}>
                    <Link to="/dashboard-user">
                        <i className="icon icon-dasbhoard" />
                        <span>Dashboard</span>
                    </Link>
                </Menu.Item>
            )}
            {ConfigHelpers.isServiceProvider() && !ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.dashboardPrvdr}>
                    <Link to="/dashboard-prvdr">
                        <i className="icon icon-data-display" />
                        <span>Dashboard</span>
                    </Link>
                </Menu.Item>
            )}
            {ConfigHelpers.isServiceProvider() &&
                ConfigHelpers.doesUserHasOneNonOnfieldRole() && (
                    <SubMenu
                        key="ace"
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                        title={
                            <Tooltip title="Automated control & efficiency">
                                <span>
                                    {' '}
                                    <i className="icon icon-culture-calendar" />
                                    <span className="gx-mr-3">ACE</span>
                                </span>
                            </Tooltip>
                        }
                    >
                        <Menu.Item key={userViewMenuKeys.exceptions}>
                            <Link to="/main/ace/exceptions">
                                <i className="icon icon-error gx-text-warning" />
                                <span>
                                    Exceptions <Tag color="orange">Beta</Tag>
                                </span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.runs}>
                            <Link to="/main/ace/runs">
                                <i className="icon icon-breadcrumb" />
                                <span>
                                    Runs <Tag color="orange">Beta</Tag>
                                </span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.autoAssign}>
                            <Link to="/main/ace/auto-assign">
                                <FaUserAstronaut className="icon" />
                                <span>Auto assign</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.quickAssignment}>
                            <Link to="/main/ace/quick-assignment">
                                <LuUsers2 className="icon" />
                                <span>Quick Assign </span>
                            </Link>
                        </Menu.Item>
                        {featureAccess?.TMS250514964399 && (
                            <Menu.Item key={userViewMenuKeys.capacityDashboard}>
                                <Link to="/main/ace/capacity-dashboard">
                                    <i className="icon icon-contacts" />
                                    <span>Capacity Dash.</span>
                                </Link>
                            </Menu.Item>
                        )}
                    </SubMenu>
                )}

            {ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.dashboardOwner}>
                    <Link to="/dashboard-owner">
                        <i className="icon icon-data-display" />
                        <span>Dashboard</span>
                        <Tag
                            color="orange"
                            className="gx-vertical-align-middle"
                        >
                            <small>Coming soon !</small>
                        </Tag>
                    </Link>
                </Menu.Item>
            )}
            {!ConfigHelpers.isOwner() &&
                ConfigHelpers.doesUserHasOneNonOnfieldRole() && (
                    <SubMenu
                        key={userViewMenuKeys.ratings}
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                        title={
                            <span className="gx-align-items-center">
                                {' '}
                                <i className="icon icon-signup" />
                                <span className="gx-mr-3">Ratings</span>
                            </span>
                        }
                    >
                        {
                            <Menu.Item key={userViewMenuKeys.taskWise}>
                                <Link to="/setup/ratings/task-wise">
                                    <i className="icon icon-data-display" />

                                    <span>Site Ratings</span>
                                </Link>
                            </Menu.Item>
                        }
                        {ConfigHelpers.isServiceProvider() && (
                            <Menu.Item key={userViewMenuKeys.dayWise}>
                                <Link to="/setup/ratings/day-wise">
                                    <i className="icon wy-sidebar-child-icon wy-icon-group-ratings" />

                                    <span>Grouped Ratings</span>
                                </Link>
                            </Menu.Item>
                        )}
                    </SubMenu>
                )}
            {ConfigHelpers.isOwner() && (
                <MenuItemGroup
                    key={userViewMenuKeys.owSetup}
                    className="gx-menu-group"
                    title="Setup"
                >
                    <SubMenu
                        key={userViewMenuKeys.configuration}
                        title={
                            <span>
                                {' '}
                                <i className="icon icon-family" />
                                <span>Configuration</span>
                            </span>
                        }
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                    >
                        <Menu.Item key={userViewMenuKeys.createServiceTypes}>
                            <Link to="/ow-setup/configuration/create-service-types">
                                <i className="icon icon-company" />
                                <span>Add ST</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.manageServiceTypes}>
                            <Link to="/ow-setup/configuration/manage-service-types">
                                <i className="icon icon-company" />
                                <span>Manage ST</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.manageSubtaskTypes}>
                            <Link to="/ow-setup/configuration/manage-subtask-types">
                                <i className="icon icon-company" />
                                <span>SubtaskTypes</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.manageFeatureFlags}>
                            <Link to="/ow-setup/configuration/manage-feature-flags">
                                <i className="icon icon-company" />
                                <span>Feature Flags</span>
                            </Link>
                        </Menu.Item>
                    </SubMenu>
                </MenuItemGroup>
            )}
            {ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.users}>
                    <Link to="/users">
                        <i className="icon icon-avatar" />
                        <span>Users</span>
                    </Link>
                </Menu.Item>
            )}
            {ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.organisations}>
                    <Link to="/organisations">
                        <i className="icon icon-company" />
                        <span>Organisations</span>
                    </Link>
                </Menu.Item>
            )}
            {ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.apiDocs}>
                    <Link to="/api-docs">
                        <i className="icon icon-company" />
                        <span>APIs</span>
                    </Link>
                </Menu.Item>
            )}
            {!ConfigHelpers.isUserOnfield() && !ConfigHelpers.isOwner() && (
                <SubMenu
                    key={userViewMenuKeys.insights}
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            {' '}
                            <i className="icon icon-chart-radar" />
                            <span className="gx-mr-3">Insights</span>
                        </span>
                    }
                >
                    {/* <Menu.Item key="setup/insights/visits">
                  <Link to="/setup/insights/visits"><i className="icon icon-visits"/>
                    <span>Visits</span>
                  </Link>
                </Menu.Item> */}

                    <Menu.Item key={userViewMenuKeys.serviceMetrics}>
                        <Link to="/setup/insights/serviceMetrics">
                            <i className="icon icon-map-directions" />

                            <Tag style={{ height: '20px' }} color="purple">
                                Coming soon!
                            </Tag>
                            <span>Service metrics</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key={userViewMenuKeys.visitMap}>
                        <Link to="/setup/insights/visitMap">
                            <i className="icon icon-map-google" />
                            <span>Visit map</span>
                        </Link>
                    </Menu.Item>

                    {featureAccess?.TMS250613586199 && (
                        <Menu.Item key={userViewMenuKeys.siteMap}>
                            <Link to="/setup/insights/siteMap">
                                <i className="icon icon-map-drawing" />
                                <span>Site map</span>
                            </Link>
                        </Menu.Item>
                    )}

                    <Menu.Item key={userViewMenuKeys.taskUpdates}>
                        <Link to="/setup/insights/taskUpdates">
                            <i className="icon icon-callout" />
                            <span>Task updates</span>
                        </Link>
                    </Menu.Item>

                    {ConfigHelpers.isServiceProvider() && (
                        <Menu.Item key={userViewMenuKeys.projects}>
                            <Link to="/setup/insights/projects">
                                <i className="icon icon-home" />
                                <span>Projects</span>
                            </Link>
                        </Menu.Item>
                    )}
                </SubMenu>
            )}
            {!ConfigHelpers.isOwner() &&
                ConfigHelpers.orgHasSbtskType() &&
                ConfigHelpers.hasAccessToJobOffers() && (
                    <Menu.Item key={userViewMenuKeys.jobBroadcasts}>
                        <Link
                            to={getLinktoObject('/job-broadcasts', {
                                filters: {
                                    task_date: getCurrentDay(),
                                },
                            })}
                        >
                            <i className="icon icon-alert" />
                            <span>Job offers</span>
                        </Link>
                    </Menu.Item>
                )}
            {!ConfigHelpers.isOwner() && ConfigHelpers.orgHasSbtskType() && (
                <Menu.Item key={userViewMenuKeys.myTasks}>
                    <Link
                        to={getLinktoObject('/my-tasks', {
                            filters: {
                                sbtsk_due_day: ['today'],
                            },
                        })}
                    >
                        <i className="icon icon-ckeditor" />
                        <span>My tasks</span>
                    </Link>
                </Menu.Item>
            )}
            {ConfigHelpers.isUserOnfield() && (
                <Menu.Item key={userViewMenuKeys.myLeaves}>
                    <Link to="/my-leaves">
                        <i className="icon icon-contacts" />
                        <span>My leaves</span>
                    </Link>
                </Menu.Item>
            )}
            {!ConfigHelpers.isOwner() &&
                ((ConfigHelpers.isServiceProvider() &&
                    ConfigHelpers.hasAccessToDeploymentRequests()) ||
                    ConfigHelpers.getDeploymentRequestsRights().read()) && (
                    <Menu.Item key={userViewMenuKeys.deploymentRequests}>
                        <Link to="/deployment-requests">
                            <i className="icon icon-calendar" />
                            <span>Dep. Requests</span>
                        </Link>
                    </Menu.Item>
                )}
            {ConfigHelpers.isUserOnfield() && (
                <SubMenu
                    key={userViewMenuKeys.inventory}
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            {' '}
                            <i className="icon wy-sidebar-icon wy-icon-manage-inventory" />
                            <span className="gx-mr-3">Inventory</span>
                        </span>
                    }
                >
                    <Menu.Item key={userViewMenuKeys.myInventory}>
                        <Link to="/main/inventory/my-inventory">
                            <i className="icon icon-contacts" />
                            <span>My Inventory</span>
                        </Link>
                    </Menu.Item>
                </SubMenu>
            )}

            {!ConfigHelpers.isUserOnfield() && !ConfigHelpers.isOwner() && (
                <SubMenu
                    key={userViewMenuKeys.attendance}
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            {' '}
                            <i className="icon icon-auth-screen" />
                            <span className="gx-mr-3">Attendance</span>
                        </span>
                    }
                >
                    <Menu.Item key={userViewMenuKeys.leaves}>
                        <Link to="/setup/attendance/leaves">
                            <i className="icon icon-contacts" />
                            <span>Leaves M</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key={userViewMenuKeys.assignment}>
                        <Link to="/setup/attendance/assignment">
                            <i className="icon icon-ckeditor" />
                            <span>Assignment</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key={userViewMenuKeys.siteAttendance}>
                        <Link to="/setup/attendance/attendance">
                            <i className="icon icon-datepicker" />
                            <span>Site Attendance</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key={userViewMenuKeys.officeAttendance}>
                        <Link to="/setup/attendance/office_attendance">
                            <i className="icon icon-wysiwyg" />
                            <span>Office Attendance</span>
                        </Link>
                    </Menu.Item>

                    {/* <Menu.Item key="setup/attendance/report">
                  <Link to="/setup/attendance/report"><i className="icon icon-wysiwyg"/>
                    <span>Report</span>
                  </Link>
                </Menu.Item> */}

                    <Menu.Item key={userViewMenuKeys.dailyReport}>
                        <Link to="/setup/attendance/daily_report">
                            <i className="icon icon-ckeditor" />
                            <span>Daily Report</span>
                        </Link>
                    </Menu.Item>

                    <Menu.Item key={userViewMenuKeys.rangeReport}>
                        <Link to="/setup/attendance/range-report">
                            <i className="icon icon-product-list" />
                            <span>Range Report</span>
                        </Link>
                    </Menu.Item>
                    {featureAccess?.TMS250303485281 && (
                        <Menu.Item key={userViewMenuKeys.availabilityReport}>
                            <Link to={'/setup/attendance/availability-report'}>
                                <i className="icon icon-calendar-new" />
                                <span>Availability</span>
                            </Link>
                        </Menu.Item>
                    )}
                </SubMenu>
            )}
            {!ConfigHelpers.isProductionEnv() &&
                !ConfigHelpers.isUserOnfield() &&
                !ConfigHelpers.isOwner() &&
                !ConfigHelpers.isServiceProvider() &&
                ConfigHelpers.getManageInventoryRights().read() && (
                    <SubMenu
                        key={userViewMenuKeys.manageInventory}
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                        title={
                            <span>
                                {' '}
                                <i className="icon wy-sidebar-icon wy-icon-manage-inventory" />
                                <span className="gx-mr-3">
                                    Manage Inventory
                                </span>
                            </span>
                        }
                    >
                        <Menu.Item key={userViewMenuKeys.manageSkus}>
                            <Link to="/main/manage-inventory/manage-skus">
                                <i className="icon wy-sidebar-child-icon wy-icon-manage-sku" />
                                <span>Manage SKUs</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.manageSpares}>
                            <Link to="/main/manage-inventory/manage-spares">
                                <i className="icon wy-sidebar-child-icon wy-icon-manage-spares" />
                                <span>Manage Spares</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.manageWarehouse}>
                            <Link to="/main/manage-inventory/manage-warehouse">
                                <i className="icon wy-sidebar-child-icon wy-icon-manage-warehouse" />
                                <span>Manage Warehouse</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.manageStock}>
                            <Link to="/main/manage-inventory/manage-stock">
                                <i className="icon wy-sidebar-child-icon wy-icon-manage-stock" />
                                <span>Manage Stock</span>
                            </Link>
                        </Menu.Item>
                    </SubMenu>
                )}
            {!ConfigHelpers.isProductionEnv() &&
                !ConfigHelpers.isUserOnfield() &&
                !ConfigHelpers.isOwner() &&
                (ConfigHelpers.getStockTransferRights().read() ||
                    ConfigHelpers.isServiceProvider()) && (
                    <SubMenu
                        key={userViewMenuKeys.stockTransfer}
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                        title={
                            <span>
                                {' '}
                                <i className="icon wy-sidebar-icon wy-icon-stock-transfer" />
                                <span className="gx-mr-3">Stock Transfer</span>
                            </span>
                        }
                    >
                        {!ConfigHelpers.isServiceProvider() && (
                            <Menu.Item key={userViewMenuKeys.interWarehouse}>
                                <Link to="/main/stock-transfer/inter-warehouse">
                                    <i className="icon icon-contacts" />
                                    <span>Inter Warehouse</span>
                                </Link>
                            </Menu.Item>
                        )}
                        {!ConfigHelpers.isServiceProvider() && (
                            <Menu.Item key={userViewMenuKeys.toSp}>
                                <Link to="/main/stock-transfer/to-sp">
                                    <i className="icon icon-contacts" />
                                    <span>To SP</span>
                                </Link>
                            </Menu.Item>
                        )}
                        {!ConfigHelpers.isServiceProvider() && (
                            <Menu.Item key={userViewMenuKeys.transferhistory}>
                                <Link to="/main/stock-transfer/transfer-history">
                                    <TbTransform className={`icon`} />
                                    <span>Transfer History</span>
                                </Link>
                            </Menu.Item>
                        )}{' '}
                        {ConfigHelpers.isServiceProvider() && (
                            <Menu.Item key={userViewMenuKeys.incomingTransfer}>
                                <Link to="/main/stock-transfer/incoming-transfer">
                                    <TbTransferIn className="icon" />
                                    <span>Incoming Transfers</span>
                                </Link>
                            </Menu.Item>
                        )}
                        {ConfigHelpers.isServiceProvider() && (
                            <Menu.Item key={userViewMenuKeys.toTechnician}>
                                <Link to="/main/stock-transfer/to-technician">
                                    <i className="icon icon-contacts" />
                                    <span>To Technician</span>
                                </Link>
                            </Menu.Item>
                        )}
                    </SubMenu>
                )}
            {!ConfigHelpers.isProductionEnv() &&
                !ConfigHelpers.isUserOnfield() &&
                !ConfigHelpers.isOwner() &&
                ConfigHelpers.isServiceProvider() && (
                    <SubMenu
                        key={userViewMenuKeys.manageInventory}
                        popupClassName={getNavStyleSubMenuClass(navStyle)}
                        title={
                            <span>
                                {' '}
                                <i className="icon icon-wysiwyg" />
                                <span className="gx-mr-3">
                                    Manage Inventory
                                </span>
                            </span>
                        }
                    >
                        <Menu.Item key={userViewMenuKeys.manageWarehouse}>
                            <Link to="/main/manage-inventory/manage-warehouse">
                                <i className="icon wy-sidebar-child-icon wy-icon-manage-warehouse" />
                                <span>Manage Warehouse</span>
                            </Link>
                        </Menu.Item>
                        <Menu.Item key={userViewMenuKeys.spInventoryMaster}>
                            <Link to="/main/manage-inventory/sp-inventory-master">
                                <i className="icon wy-sidebar-child-icon wy-icon-sp-inventory-master" />
                                <span>SP Inventory Master</span>
                            </Link>
                        </Menu.Item>
                    </SubMenu>
                )}
            {ConfigHelpers.isServiceProvider() &&
                !ConfigHelpers.isUserOnfield() &&
                !ConfigHelpers.isOwner() && (
                    <Menu.Item key={userViewMenuKeys.customerAccess}>
                        <Link to="/customer-access">
                            <i className="icon icon-navigation" />
                            <span>Customer access</span>
                        </Link>
                    </Menu.Item>
                )}
            {ConfigHelpers.isServiceProvider() && !ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.customerRequests}>
                    <Link to="/customer-requests">
                        <i className="icon icon-breadcrumb" />
                        <span>Customer requests</span>
                    </Link>
                </Menu.Item>
            )}
            {/* <Menu.Item key="my-team">
              <Link to="/my-team"><i className="icon icon-team"/>
                <span>My Team</span>
              </Link>
            </Menu.Item> */}
            {config_data?.srvc_list?.length > 0 &&
                access_service_routes?.length > 0 && (
                    <>
                        <MenuItemGroup
                            key={userViewMenuKeys.serviceRequests}
                            className="gx-menu-group"
                            title="Service requests"
                        ></MenuItemGroup>
                        {access_service_routes.map((singleSrvcRoute) => {
                            var singleSrvcMenu = ConfigHelpers.getSrvcRoute(
                                singleSrvcRoute.menu_id
                            );
                            return (
                                <>
                                    {singleSrvcMenu != undefined && (
                                        <Menu.Item
                                            key={`${userViewMenuKeys.services}/${singleSrvcMenu.srvc_id}`}
                                        >
                                            <Link
                                                to={`/services/${singleSrvcMenu.srvc_id}`}
                                            >
                                                <i
                                                    className={`icon ${singleSrvcMenu.srvc_icon}`}
                                                />
                                                <span>
                                                    {singleSrvcMenu.srvc_title}
                                                </span>
                                            </Link>
                                        </Menu.Item>
                                    )}
                                </>
                            );
                        })}
                    </>
                )}
            {!ConfigHelpers.isServiceProvider() &&
                !ConfigHelpers.isUserOnfield() &&
                !ConfigHelpers.isOwner() && (
                    <>
                        <MenuItemGroup
                            key={userViewMenuKeys.more}
                            className="gx-menu-group"
                            title="More"
                        ></MenuItemGroup>
                        {/* {
                  ['test','dev'].includes(process.env.REACT_APP_RELEASE_ENV) &&
                  <Menu.Item key="warehouse/12">
                    <Link to="/warehouse/12"><i className="icon icon-extra-components"/>
                      <span>Parts <Tag style={{height:'20px'}} color="purple">Coming soon!</Tag> </span>
                    </Link>
                  </Menu.Item>
                } */}

                        <Menu.Item key={userViewMenuKeys.customer}>
                            <Link to="/customer">
                                <i className="icon icon-avatar" />
                                <span>Customers</span>
                            </Link>
                        </Menu.Item>
                    </>
                )}
            {!ConfigHelpers.isUserOnfield() &&
                ConfigHelpers.hasOnfieldAppAccess() && (
                    <Menu.Item key={userViewMenuKeys.technicianAppAccess}>
                        <Link to="/technician-app-access">
                            <MobileOutlined />
                            <span>Technician app</span>
                        </Link>
                    </Menu.Item>
                )}
            {/* {
              <Menu.Item key="tutorials">
                <Link to="/tutorials"><i className="icon icon-graduation"/>
                  <span>Tutorials</span>
                </Link>
              </Menu.Item>
            } */}
            {/* {
              <Menu.Item key="version-log">
                <Link to="/version-log"><i className="icon icon-timeline-new"/>
                  <span>Version log</span>
                </Link>
              </Menu.Item>
            }, */}

            {ConfigHelpers.isServiceProvider() ? (
                <SubMenu
                    key={userViewMenuKeys.myAccount}
                    popupClassName={getNavStyleSubMenuClass(navStyle)}
                    title={
                        <span>
                            <i className="icon icon-setting" />
                            <span className="gx-mr-3">Account Settings</span>
                        </span>
                    }
                >
                    <Menu.Item key={userViewMenuKeys.profileDetails}>
                        <Link to="/main/my-account/my-profile/profile-details">
                            <i className="icon icon-avatar" />
                            <span>My Profile</span>
                        </Link>
                    </Menu.Item>
                    <Menu.Item key={userViewMenuKeys.mySettings}>
                        <Link to="/main/my-account/my-settings">
                            <i className="icon icon-extra-components" />
                            <span>My Settings</span>
                        </Link>
                    </Menu.Item>
                </SubMenu>
            ) : (
                <Menu.Item key={userViewMenuKeys.profileDetails}>
                    <Link to="/my-profile/profile-details">
                        <i className="icon icon-avatar" />
                        <span>My Account</span>
                    </Link>
                </Menu.Item>
            )}

            <Menu.Item key={userViewMenuKeys.changeLog}>
                <Link to="/change-log">
                    <i className="icon icon-apps-new" />
                    <span>Change log</span>
                </Link>
            </Menu.Item>
            {ConfigHelpers.isUserAdmin(roles) && !ConfigHelpers.isOwner() && (
                <Menu.Item key={userViewMenuKeys.toggleRole}>
                    <Link to="/toggle-role">
                        <i className="icon icon-transfer" />
                        <span>Admin view</span>
                    </Link>
                </Menu.Item>
            )}
        </Menu>
    );
};

export default UserViewMenu;
