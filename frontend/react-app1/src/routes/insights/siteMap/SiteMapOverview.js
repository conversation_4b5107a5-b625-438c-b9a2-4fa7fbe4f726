import React, { useState, useEffect } from 'react';
import { Tabs, Spin } from 'antd';
import http_utils from '../../../util/http_utils';
import { handleFilterClearDateIfNull } from '../../../util/helpers';

const { TabPane } = Tabs;

const SiteMapOverview = ({ filterObject, searchQuery }) => {
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState('');

    useEffect(() => {
        initViewData();
    }, [filterObject, searchQuery]);

    const initViewData = () => {
        console.log('SiteMapOverview :: initViewData :: called');
        setIsLoadingViewData(true);
        setError('');

        handleFilterClearDateIfNull(filterObject);

        // Prepare API parameters
        const params = {
            filters: filterObject,
            search_query: searchQuery || '',
        };

        const onComplete = (resp) => {
            console.log(
                'SiteMapOverview :: initViewData :: API response:',
                resp
            );
            setIsLoadingViewData(false);
            setViewData(resp.data);
            setError('');
        };

        const onError = (error) => {
            console.error(
                'SiteMapOverview :: initViewData :: API error:',
                error
            );
            setIsLoadingViewData(false);
            setError(http_utils.decodeErrorToMessage(error));
        };

        // Call the API
        http_utils.performGetCall(
            '/site-map/overview',
            params,
            onComplete,
            onError
        );
    };

    if (isLoadingViewData) {
        return (
            <div
                className="gx-d-flex gx-justify-content-center gx-align-items-center"
                style={{ height: '400px' }}
            >
                <Spin size="large" />
                <div className="gx-ml-2">Loading site data...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div
                className="gx-d-flex gx-justify-content-center gx-align-items-center"
                style={{ height: '400px' }}
            >
                <p className="gx-text-red">{error}</p>
            </div>
        );
    }

    if (!viewData) {
        return (
            <div
                className="gx-d-flex gx-justify-content-center gx-align-items-center"
                style={{ height: '400px' }}
            >
                <p>No data available</p>
            </div>
        );
    }

    const siteData = viewData.sites || [];

    return (
        <div className="gx-p-1">
            <Tabs defaultActiveKey="marker" className="gx-text-capitalize">
                <TabPane
                    key="marker"
                    style={{ height: 'calc(100vh - 250px)' }}
                    tab={
                        <span>
                            <i className="icon icon-map-street-view"></i> MARKER
                        </span>
                    }
                >
                    <div className="gx-d-flex gx-justify-content-center gx-align-items-center gx-h-100">
                        <div className="gx-text-center">
                            <h3>Map View</h3>
                            <p>Showing {siteData.length} sites</p>
                            <div className="gx-mt-3">
                                {siteData.map((site) => (
                                    <div
                                        key={site.site_id}
                                        className="gx-mb-2 gx-p-2 gx-border"
                                    >
                                        <strong>Pincode: {site.pincode}</strong>
                                        <br />
                                        Site ID: {site.site_id}
                                        <br />
                                        <small>
                                            {site.site_name} - {site.address}
                                        </small>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </TabPane>

                <TabPane
                    key="heat_map"
                    style={{ height: 'calc(100vh - 250px)' }}
                    tab={
                        <span>
                            <i className="icon icon-map-clustering gx-mr-1"></i>{' '}
                            HEAT MAP
                        </span>
                    }
                >
                    <div className="gx-d-flex gx-justify-content-center gx-align-items-center gx-h-100">
                        <div className="gx-text-center">
                            <h3>Heat Map View</h3>
                            <p>
                                Heat map visualization will be implemented here
                            </p>
                            <p>Showing {siteData.length} sites</p>
                        </div>
                    </div>
                </TabPane>
            </Tabs>
        </div>
    );
};

export default SiteMapOverview;
