import { DatePicker } from 'antd';
import moment from 'moment';
import { getPresetRangesForRangeDatePicker } from '../../../util/helpers';
import ConfigHelpers from '../../../util/ConfigHelpers';

let brandsFilter = [];
let verticalFilter = [];
if (ConfigHelpers.isServiceProvider()) {
    brandsFilter.push({
        key: 'brands',
        label: 'Brands',
        widget: 'select',
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    });
    verticalFilter.push({
        key: 'verticals_list',
        label: 'Vertical (Select one vertical)',
        widget: 'select',
        quick: 'true',
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    });
}

const filters = [
    {
        key: 'site_creation_date',
        label: 'Site Creation Date',
        widget: DatePicker.RangePicker,
        widgetProps: {
            ranges: getPresetRangesForRangeDatePicker(),
            defaultValue: [moment().startOf('day'), moment()],
        },
    },
    {
        key: 'locations',
        label: 'Request location group',
        widget: 'select',
        placeholder: 'Select..',
        // "quick" : true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    },
    ...brandsFilter,
    ...verticalFilter,
];

export { filters };
