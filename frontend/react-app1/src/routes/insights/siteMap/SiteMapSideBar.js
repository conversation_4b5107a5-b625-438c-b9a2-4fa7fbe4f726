import React from 'react';
import CustomScrollbars from '../../../util/CustomScrollbars';
import QuickFilters from '../../../components/wify-utils/crud/overview/QuickFilters';
import { FaMapMarkedAlt } from 'react-icons/fa';

const SiteMapSideBar = ({ filters, onFilterChange, activeFilters }) => {
    return (
        <div className="gx-module-side">
            <div className="gx-module-side-header">
                <div className="gx-module-logo gx-d-flex gx-align-items-center gx-justify-content-between wy-gap-10 gx-module-logo">
                    <i>
                        <FaMapMarkedAlt />
                    </i>
                    <span>Site Map</span>
                </div>
            </div>

            <div className="gx-module-side-content">
                <CustomScrollbars className="gx-module-side-scroll">
                    <QuickFilters
                        filters={filters}
                        onFilterChange={onFilterChange}
                        activeFilters={activeFilters}
                    />
                </CustomScrollbars>
            </div>
        </div>
    );
};

export default SiteMapSideBar;
