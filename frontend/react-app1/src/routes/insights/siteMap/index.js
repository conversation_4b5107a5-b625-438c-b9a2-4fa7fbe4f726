import React, { useState, useEffect } from 'react';
import { Drawer } from 'antd';
import CustomScrollbars from '../../../util/CustomScrollbars';
import SiteMapOverview from './SiteMapOverview';
import AppModuleHeader from '../../../components/AppModuleHeader';
import SiteMapSideBar from './SiteMapSideBar';
import { filters } from './filters';
import { getFilterFormMetaFilledWithApiData } from '../../../components/wify-utils/crud/overview/filter_helpers';
import {
    getAnyObjectFrFilter,
    handleFilterClearDateIfNull,
} from '../../../util/helpers';
import http_utils from '../../../util/http_utils';
import CircularProgress from '../../../components/CircularProgress';

const SiteMap = () => {
    const [filterConfig, setFilterConfig] = useState({
        activeFilters: {},
        searchFilter: '',
        drawerState: false,
    });
    const [viewData, setViewData] = useState(undefined);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        initViewData();
    }, []);

    const initViewData = () => {
        if (viewData == undefined && !isLoadingViewData) {
            setIsLoadingViewData(true);
            handleFilterClearDateIfNull(filterConfig.activeFilters);
            var params = {
                filters: filterConfig.activeFilters,
            };
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
                setError('');
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(
                '/site-map/overview_proto',
                params,
                onComplete,
                onError
            );
        }
    };

    const resetFilter = () => {
        setFilterConfig((prev) => ({
            ...prev,
            activeFilters: {},
        }));
        // Re-initialize view data after filter reset
        setViewData(undefined);
    };

    useEffect(() => {
        if (
            filterConfig.activeFilters &&
            Object.keys(filterConfig.activeFilters).length === 0 &&
            viewData === undefined
        ) {
            initViewData();
        }
    }, [filterConfig.activeFilters]);

    const getFilters = () => {
        var staticFilters = [...filters];
        var defaultAnyMeta = getAnyObjectFrFilter();
        var filtersFrmViewData = viewData?.filters_proto;
        var finalFilter = getFilterFormMetaFilledWithApiData(
            staticFilters,
            defaultAnyMeta,
            filtersFrmViewData
        );
        return finalFilter;
    };

    const getExportMenuData = () => {
        return [
            {
                key: 'export-csv',
                label: 'Export CSV',
                icon: 'icon-export',
                onClick: () => {
                    console.log('Export CSV clicked');
                    // TODO: Implement CSV export functionality
                },
            },
            {
                key: 'export-pdf',
                label: 'Export PDF',
                icon: 'icon-pdf',
                onClick: () => {
                    console.log('Export PDF clicked');
                    // TODO: Implement PDF export functionality
                },
            },
        ];
    };

    if (isLoadingViewData) {
        return (
            <div className="gx-loader-view gx-loader-position">
                <CircularProgress />
            </div>
        );
    } else if (viewData == null) {
        return <p className="gx-text-red">{error}</p>;
    }

    return (
        <div className="gx-main-content">
            <div className="gx-app-module">
                {/* Mobile Drawer */}
                <div className="gx-d-block gx-d-lg-none">
                    <Drawer
                        placement="left"
                        closable={false}
                        visible={filterConfig.drawerState}
                        onClose={() =>
                            setFilterConfig((prevConfig) => ({
                                ...prevConfig,
                                drawerState: false,
                            }))
                        }
                    >
                        <SiteMapSideBar
                            filters={getFilters()}
                            onFilterChange={(newFilters) =>
                                setFilterConfig((prev) => ({
                                    ...prev,
                                    activeFilters: newFilters,
                                }))
                            }
                            activeFilters={filterConfig.activeFilters}
                        />
                    </Drawer>
                </div>

                {/* Desktop Sidebar */}
                <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                    <SiteMapSideBar
                        filters={getFilters()}
                        onFilterChange={(newFilters) =>
                            setFilterConfig((prev) => ({
                                ...prev,
                                activeFilters: newFilters,
                            }))
                        }
                        activeFilters={filterConfig.activeFilters}
                    />
                </div>

                {/* Main Content */}
                <div className="gx-module-box">
                    <div className="gx-module-box-header">
                        <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                            <i
                                className="icon icon-filter gx-icon-btn"
                                aria-label="Menu"
                                onClick={() =>
                                    setFilterConfig((prevConfig) => ({
                                        ...prevConfig,
                                        drawerState: true,
                                    }))
                                }
                            />
                        </span>

                        <AppModuleHeader
                            placeholder="Search by Site ID, Pincode..."
                            currValue={filterConfig.searchFilter}
                            optionsMenuData={getExportMenuData()}
                            onChange={(value) =>
                                setFilterConfig((prevConfig) => ({
                                    ...prevConfig,
                                    searchFilter: value,
                                }))
                            }
                        />
                    </div>

                    <div className="gx-module-box-content gx-px-4 gx-py-3">
                        <div className="gx-d-none gx-d-lg-block gx-h-100">
                            <CustomScrollbars>
                                <SiteMapOverview
                                    filterObject={filterConfig.activeFilters}
                                    searchQuery={filterConfig.searchFilter}
                                />
                            </CustomScrollbars>
                        </div>
                        <div className="gx-d-lg-none gx-d-block gx-h-100">
                            <SiteMapOverview
                                filterObject={filterConfig.activeFilters}
                                searchQuery={filterConfig.searchFilter}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SiteMap;
