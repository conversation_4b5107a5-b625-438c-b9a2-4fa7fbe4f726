import React from 'react';
import GoogleMapComponent from '../../../components/wify-utils/GoogleMapComponent';
import { getCenterLocFrIndMap } from '../../../util/helpers';

const SiteMapComponent = ({ siteData }) => {
    const getCustomMarkerOptions = (singleLocationDetails) => {
        const siteName = singleLocationDetails.site_name || 'Site';
        const firstLetter = siteName.charAt(0).toUpperCase();
        return {
            label: {
                text: firstLetter,
                color: 'white',
            },
            title: siteName,
        };
    };

    const getInfoWindowContent = (singleLocationDetails) => {
        return (
            'Site ID: <b>' +
            singleLocationDetails.site_id +
            '</b><br/><br/>' +
            'Site Name: <b>' +
            (singleLocationDetails.site_name || 'N/A') +
            '</b><br/><br/>' +
            'Pincode: <b>' +
            singleLocationDetails.pincode +
            '</b><br/><br/>' +
            'Address: <b>' +
            (singleLocationDetails.address || 'N/A') +
            '</b>'
        );
    };

    // Transform site data to match the expected format for GoogleMapComponent
    const transformedSiteData =
        siteData?.map((site) => ({
            ...site,
            geocoding_location_data: {
                location: {
                    lat: site.latitude || site.lat || 0,
                    lng: site.longitude || site.lng || 0,
                },
            },
        })) || [];

    return (
        <GoogleMapComponent
            center={getCenterLocFrIndMap()}
            zoom={5}
            markerMapMode={true}
            locations={transformedSiteData}
            customMarkerOptionsCallback={getCustomMarkerOptions}
            infoWindowContentCallback={getInfoWindowContent}
        />
    );
};

export default SiteMapComponent;
