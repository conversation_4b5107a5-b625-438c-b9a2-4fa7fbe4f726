import React from 'react';
import GoogleMapComponent from '../../../components/wify-utils/GoogleMapComponent';
import { getCenterLocFrIndMap } from '../../../util/helpers';

const SiteHeatMapComponent = ({ siteData }) => {
    // Transform site data to match the expected format for GoogleMapComponent heat map
    const transformedSiteData = siteData?.map((site) => ({
        ...site,
        geocoding_location_data: {
            location: {
                lat: site.latitude || site.lat || 0,
                lng: site.longitude || site.lng || 0,
            },
        },
    })) || [];

    return (
        <GoogleMapComponent
            center={getCenterLocFrIndMap()}
            zoom={5}
            markerMapMode={false} // This enables heat map mode
            locations={transformedSiteData}
        />
    );
};

export default SiteHeatMapComponent;
