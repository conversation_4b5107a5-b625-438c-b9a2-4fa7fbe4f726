import React, { Component } from 'react';
import { Alert, message, Modal } from 'antd';
import CSVExporter from '../../../components/wify-utils/CSVExporter';
import ConfigHelpers from '../../../util/ConfigHelpers';
import {
    convertMomentToLocalDateString,
    convertUTCToDisplayTime,
    getCurrentDateAndTimeFrDisplay,
} from '../../../util/helpers';

const submitUrl = '/availability-report/export';
class ExporterModal extends Component {
    constructor(props) {
        super(props);
    }

    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        error: '',
    };
    state = this.initState;

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.showEditor != this.props.showEditor) {
            this.setState({
                render_helper: !this.state.render_helper,
                visible: this.props.showEditor,
            });
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }

    getAppliedFilterDateRange() {
        let selectedFilterDateRange = getCurrentDateAndTimeFrDisplay();
        let filterDate = this.props.filters;
        if (filterDate.days) {
            let dateFrom = filterDate.days[0];
            let dateTo = filterDate.days[1];
            selectedFilterDateRange =
                ' ' +
                convertUTCToDisplayTime(dateFrom, true) +
                ' To ' +
                convertUTCToDisplayTime(dateTo, true);
        }
        return selectedFilterDateRange;
    }

    getMetaFrExporter() {
        const columns =
            this.props.exportType === 'export_raw_data'
                ? this.getRawDataMeta().fields
                : this.getSummaryDataMeta().fields;
        return {
            title: 'Export User Availability',
            email_id: `${ConfigHelpers.getUserEmailId()}`,
            subject: `WIFY TMS User Availability dump ${this.getAppliedFilterDateRange()}`,
            filters:
                {
                    ...this.props.filters,
                    export_type: this.props.exportType || null,
                } || {},
            section_wise_meta: [],
            filename: `Orders_Without_Hub_${getCurrentDateAndTimeFrDisplay()}`,
            column_names: columns,
        };
    }
    //write function if exporttype is raw date then col will be technicname, day,timeslot,avavailable
    //else col name is technicina name,day,available
    getRawDataMeta = () => {
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'technician_name',
                    label: 'Technician name',
                },
                {
                    key: 'available',
                    label: 'Available',
                },
                {
                    key: 'day',
                    label: 'Day',
                },
                {
                    key: 'time_slots',
                    label: 'Time Slots',
                },
                {
                    key: 'updated_on',
                    label: 'Updated On',
                },
            ],
        };
        return meta;
    };
    getSummaryDataMeta = () => {
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'technician_name',
                    label: 'Technician name',
                },
                {
                    key: 'available',
                    label: 'Available',
                },
                {
                    key: 'day',
                    label: 'Day',
                },
            ],
        };
        return meta;
    };

    getLeaveInfoMeta = () => {
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'technician_name',
                    label: 'Technician name',
                },
                {
                    key: 'technician_role',
                    label: 'Role',
                },
                {
                    key: 'leave_type',
                    label: 'Leave type',
                },
                {
                    key: 'start_and_end_date',
                    label: 'Start date - End date',
                },
                {
                    key: 'remarks',
                    label: 'Remarks',
                },

                {
                    key: 'added_by',
                    label: 'Added by',
                },
                {
                    key: 'approved_by',
                    label: 'Responsible',
                },
                {
                    key: 'time',
                    label: 'Time',
                },
                {
                    key: 'status',
                    label: 'Status',
                },
            ],
        };
        return meta;
    };

    render() {
        const { isFormSubmitting, visible } = this.state;

        return visible ? (
            <Modal
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={700}
                // style={{
                //     marginTop:'-70px'
                // }}
                // bodyStyle={{
                //     minHeight:'85vh',
                //     padding:'18px',
                //     paddingTop: '0px'
                // }}
                footer={null}
                onCancel={this.handleCancel}
            >
                <CSVExporter
                    // demoMode
                    exportMeta={this.getMetaFrExporter()}
                    submitUrl={submitUrl}
                    onSubmitComplete={(resp) => {
                        message.success(
                            'Email will be sent shortly, Export data request added successfully'
                        );
                        this.handleCancel();
                    }}
                    //isFiltersAppliedHide={true}
                    noColumnSelection={true}
                    showColumnInfo={true}
                />
            </Modal>
        ) : (
            <></>
        );
    }
}

export default ExporterModal;
