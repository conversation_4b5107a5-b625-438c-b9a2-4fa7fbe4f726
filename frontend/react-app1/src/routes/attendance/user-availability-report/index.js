import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Drawer, <PERSON>u, <PERSON>, Tag } from 'antd';
import { useHistory, useLocation } from 'react-router-dom';
import CustomScrollbars from '../../../util/CustomScrollbars';
import AppModuleHeader from '../../../components/AppModuleHeader';
import { filters } from './filters';
import PagedApiListView from '../../../components/wify-utils/crud/overview/PagedApiListView';
import Avatar from 'antd/lib/avatar/avatar';
import { SideBar } from './SideBar';

import {
    convertUTCToDisplayTime,
    getAnyObjectFrFilter,
    getLinktoObject,
    getRandomBgColor,
    handleFilterClearDateIfNull,
    isMobileView,
} from '../../../util/helpers';
import { getFilterFormMetaFilledWithApiData } from '../../../components/wify-utils/crud/overview/filter_helpers';
import CircularProgress from '../../../components/CircularProgress';
import http_utils from '../../../util/http_utils';
import IconWithTextCard from '../../../components/IconWithTextCard';
import { DownloadOutlined, EditFilled, EyeFilled } from '@ant-design/icons';
import ExporterModal from './ExporterModal';
import { render } from '@testing-library/react';

const StatusVsColor = {
    Available: 'success',
    Limited: 'processing',
    Offline: 'error',
};

const protoUrl = '/availability-report';

const CapacityWiseAvailability = () => {
    const history = useHistory();
    const location = useLocation();

    // Helper functions to get initial state
    // Get filters object from URL
    const getFiltersFrmSearch = () => {
        const parsedFilter = new URLSearchParams(location.search).get(
            'filters'
        );
        return parsedFilter ? JSON.parse(parsedFilter) : {};
    };

    // Get search query string from URL
    const getSearchFromUrl = () => {
        const searchFrmLink = new URLSearchParams(location.search).get('query');
        return searchFrmLink || '';
    };

    // State management
    const [activeFilters, setActiveFilters] = useState(getFiltersFrmSearch());
    const [searchFilter, setSearchFilter] = useState(getSearchFromUrl());
    const [drawerState, setDrawerState] = useState(false);
    const [showEditor, setShowEditor] = useState(false);
    const [showItemEditor, setShowItemEditor] = useState(false);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState('');
    const [showExporter, setShowExporter] = useState(false);
    const [editorItem, setEditorItem] = useState(undefined);
    const [customPageSize] = useState(true);
    const [exportType, setExportType] = useState(null);

    // Initialize view data
    const initViewData = () => {
        if (viewData === undefined && !isLoadingViewData) {
            setIsLoadingViewData(true);
            handleFilterClearDateIfNull(activeFilters);

            const params = {
                filters: activeFilters,
                search_query: '',
            };

            const onComplete = (resp) => {
                console.log('resp data', resp.data);
                setIsLoadingViewData(false);
                setViewData(resp.data);
                setError('');
            };

            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };

            http_utils.performGetCall(
                protoUrl + '/overview-proto',
                params,
                onComplete,
                onError
            );
        }
    };

    // Effect to initialize data on mount
    useEffect(() => {
        initViewData();
    }, [initViewData]);

    // Handler functions
    const resetFilter = () => {
        setActiveFilters({});
    };

    const handleFilterChange = (newFilterObject) => {
        setActiveFilters({ ...newFilterObject });
    };

    const handleSearchChange = (query) => {
        setSearchFilter(query);
    };

    const onToggleDrawer = () => {
        setDrawerState(!drawerState);
    };

    // Get filters for the component
    const getFilters = () => {
        let staticFilters = [...filters];

        const defaultAnyMeta = getAnyObjectFrFilter();
        const filtersFrmViewData = viewData?.filters_proto;
        const finalFilter = getFilterFormMetaFilledWithApiData(
            staticFilters,
            defaultAnyMeta,
            filtersFrmViewData
        );
        return finalFilter;
    };

    const onSingleRowClick =
        ((item) => {
            setEditorItem(item);
            setShowItemEditor(true);
        },
        []);

    // Configuration for PagedApiListView - updated for capacity-wise availability
    const configFrPagedApiListView = {
        dataSourceApi: '/availability-report',
        columns: [
            {
                key: 'technician_name',
                dataIndex: 'technician_name',
                title: 'Technician name',
            },
            {
                key: 'is_available',
                dataIndex: 'is_available',
                title: 'Is available',
                render: (text, record) => {
                    //if avaialble is false then show NO else show yes
                    if (text == false) {
                        return <Tag color="error"> No </Tag>;
                    } else {
                        return <Tag color="success"> Yes </Tag>;
                    }
                },
            },
            {
                key: 'day',
                dataIndex: 'day',
                title: 'Day',
            },
            {
                key: 'time_slots',
                dataIndex: 'time_slots',
                title: 'Time Slot',
            },
            {
                key: 'updated_on',
                dataIndex: 'updated_on',
                title: 'Updated on',
                render: (text, record) =>
                    record.updated_on
                        ? convertUTCToDisplayTime(record.updated_on)
                        : '-',
            },
        ],
    };

    // Export menu data
    const getExportMenuData = () => {
        return (
            <Menu>
                <Menu.Item
                    key="export_request"
                    onClick={() => {
                        setExportType('export_request');
                        setShowExporter(true);
                    }}
                >
                    <span>
                        <DownloadOutlined /> Export report
                    </span>
                </Menu.Item>
                <Menu.Item
                    key="export_raw_data"
                    onClick={() => {
                        setExportType('export_raw_data');
                        setShowExporter(true);
                    }}
                >
                    <span>
                        <DownloadOutlined /> Export raw data
                    </span>
                </Menu.Item>
            </Menu>
        );
    };

    // Dashboard data for capacity-wise availability
    const getDashboardData = () => {
        const dashboardData = viewData?.dashboardData;

        const dashboardCountData = [
            {
                count: dashboardData ? dashboardData?.total_user : 0,
                title: 'Total Users',
                color: '#ffc107',
                key: 'total_user',
            },
            {
                count: dashboardData ? dashboardData?.fully_present : 0,
                title: 'Fully Present',
                color: '#4caf50',
                key: 'fully_present',
            },
            {
                count: dashboardData ? dashboardData?.limited : 0,
                title: 'Limited',
                color: '#607d8b',
                key: 'limited',
            },
            {
                count: dashboardData ? `${dashboardData?.offline}` : 0,
                title: 'Offline',
                color: '#e57373',
                key: 'offline',
            },
        ];
        return dashboardCountData;
    };

    // Get dashboard count data
    const dashboardCount = getDashboardData();

    // Render the component
    return (
        <>
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData === undefined ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <div className="gx-main-content">
                    <div className="gx-app-module">
                        <div className="gx-d-block gx-d-lg-none">
                            <Drawer
                                placement="left"
                                closable={false}
                                visible={drawerState}
                                onClose={onToggleDrawer}
                            >
                                <SideBar
                                    filters={getFilters()}
                                    onFilterChange={handleFilterChange}
                                    activeFilters={activeFilters}
                                />
                            </Drawer>
                        </div>
                        <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                            <SideBar
                                filters={getFilters()}
                                onFilterChange={handleFilterChange}
                                activeFilters={activeFilters}
                            />
                        </div>

                        <ExporterModal
                            showEditor={showExporter}
                            filters={activeFilters}
                            exportType={exportType}
                            onClose={() => {
                                setShowExporter(false);
                                setExportType(null); 
                            }}
                        />

                        {/* Creator popup */}

                        <div className="gx-module-box">
                            <div className="gx-module-box-header">
                                <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                    <i
                                        className="icon icon-filter gx-icon-btn"
                                        aria-label="Menu"
                                        onClick={onToggleDrawer}
                                    />
                                </span>

                                <AppModuleHeader
                                    placeholder="Search by User name.."
                                    currValue={searchFilter}
                                    onChange={handleSearchChange}
                                    optionsMenuData={getExportMenuData()}
                                />
                            </div>

                            <Row className="gx-p-2 gx-bg-white gx-border">
                                {dashboardCount?.map(
                                    (singleDashboardCount, index) => (
                                        <Col xs={12} md={6} lg={6} key={index}>
                                            <IconWithTextCard
                                                className="gx-mb-0"
                                                cardColorCode={
                                                    singleDashboardCount.color
                                                }
                                                title={
                                                    singleDashboardCount.count
                                                }
                                                subTitle={
                                                    singleDashboardCount.title
                                                }
                                                cardSpace={true}
                                            />
                                        </Col>
                                    )
                                )}
                            </Row>

                            <div className="gx-module-box-content gx-px-4 gx-py-3">
                                <CustomScrollbars>
                                    <PagedApiListView
                                        {...configFrPagedApiListView}
                                        filterObject={activeFilters}
                                        searchQuery={searchFilter}
                                        //customPageSize={customPageSize}
                                        tableView
                                    />
                                </CustomScrollbars>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default CapacityWiseAvailability;
