const { AUTH_OWNER } = require('../../api_models/utils/authrizor');

const prefix = '/v1'; // for org type customer

module.exports = ({
    AUTH_ADMIN_MIDDLEWARE,
    AUTH_MIDDLEWARE,
    AUTH_CONSUMER_FEEDBACK_MIDDLEWARE,
}) => {
    return [
        {
            path: prefix + '/setup/roles/',
            route_file: './routes/v1_roles',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/srvc-req',
            route_file: './routes/v1_srvc_req',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/sub-task-type',
            route_file: './routes/v1_sbtsk_type',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/automate/pulse-tracker',
            route_file: './routes/automate-deployment/v1_pulse_tracker',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/automate/lambda-based',
            route_file: './routes/automate-deployment/v1_lambda_based_dep',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/automate/broadcast-menu',
            route_file: './routes/automate-deployment/v1_broadcast_menu',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/user/',
            route_file: './routes/v1_user',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/srvcProviders/',
            route_file: './routes/v1_srvc_prvdrs',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/dashboard/',
            route_file: './routes/v1_cust_admin_dashboard',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/cust/',
            route_file: './routes/v1_customer',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/services/',
            route_file: './routes/v1_services',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/searcher/',
            route_file: './routes/v1_searcher',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/locGroup/',
            route_file: './routes/v1_location_group',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/subtasks/',
            route_file: './routes/v1_subtasks',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/my-tasks/',
            route_file: './routes/v1_subtasks',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/onfield-user/my-tasks/',
            route_file: './routes/v1_onfield_user',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/dashboard-user/',
            route_file: './routes/v1_cust_user_dashboard',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/mock/',
            route_file: './routes/v1_mock',
            // middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/visit-map/',
            route_file: './routes/v1_visit_map',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/site-map/',
            route_file: './routes/v1_site_map',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/api/',
            route_file: './routes/v1_api_setup',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/statusGroup/',
            route_file: './routes/v1_status_group',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/attendance/',
            route_file: './routes/v1_attendance',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/version-log/',
            route_file: './routes/v1_version_log',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/user-custom-fields/',
            route_file: './routes/v1_user_custom_fields',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/capture_login_attendance',
            route_file: './routes/v1_capture_login_attendance',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/sp-custom-fields/',
            route_file: './routes/v1_sp_custom_fields',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/rate-card/',
            route_file: './routes/v1_rate_card',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/sp-authorities/',
            route_file: './routes/v1_sp_authorities',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/rating/rating-templates/',
            route_file: './routes/v1_rating_templates',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/organisations',
            route_file: './routes/v1_organisations',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/ow-setup/add-st/',
            route_file: './routes/v1_owner_add_st',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/ow-setup/manage-sbtsk',
            route_file: './routes/v1_owner_manage_sbtsk',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/ow-setup/manage-feature-flag',
            route_file: './routes/v1_feature_flag',
            middleware: [AUTH_MIDDLEWARE, AUTH_OWNER],
        },
        {
            path: prefix + '/feature-access',
            route_file: './routes/v1_feature_flag_access',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/my-profile',
            route_file: './routes/v1_my_profile',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/automation-deployment/',
            route_file: './routes/v1_automation_deployment',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/billing-fields/',
            route_file: './routes/v1_billing_fields',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/restrictions/',
            route_file: './routes/v1_restrictions',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/capture-last-seen/',
            route_file: './routes/v1_copy_last_seen_frm_redis_to_db',
        },
        {
            path: prefix + '/ratings',
            route_file: './routes/v1_ratings',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/user-settings',
            route_file: './routes/v1_user_settings',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/settings/',
            route_file: './routes/v1_settings',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/quick-assignment',
            route_file: './routes/v1_quick_assignment',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/manage-custom-fields/',
            route_file: './routes/v1_inventory_custom_field',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/cache-update-data',
            route_file: './routes/v1_cache_update',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/zones/',
            route_file: './routes/v1_zones',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/capacity/vertical-skills',
            route_file: './routes/setup-admin/capacity/v1_vertical_skills',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/capacity/service-hubs',
            route_file: './routes/v1_service_hubs',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/capacity/time-slots',
            route_file: './routes/v1_time_slots',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/capacity/availability-slots',
            route_file: './routes/v1_availability_slots',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/setup/capacity/capacity-settings',
            route_file: './routes/v1_capacity_settings',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/deployment-request/',
            route_file: './routes/v1_deployment_request.js',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/ace',
            route_file: './routes/v1_ace_actions',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/ace-capacity-dashboard',
            route_file: './routes/ace/v1_ace_cap_dashboard',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/my-availability',
            route_file: './routes/v1_ace_my_availability',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/profit-loss',
            route_file: './routes/v1_profit_loss',
            middleware: AUTH_MIDDLEWARE,
        },
        {
            path: prefix + '/availability-report',
            route_file: './routes/ace/v1_availability_report',
            middleware: AUTH_MIDDLEWARE,
        },
    ];
};
