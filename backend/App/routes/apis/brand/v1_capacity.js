var express = require('express');
var router = express.Router();
var HttpStatus = require('http-status-codes');
const { getUserContextFrmReq } = require('../../../api_models/utils/authrizor');
const {
    validateGetCapacityQuery,
    validateGetCapacityQueryInDB,
} = require('../middlewars/capacity');

const validateGetCapacityInputQuery = (req, res, next) => {
    const model = require('../../../api_models/ace/capacity_model');
    setParamsToModel(req, model);

    try {
        model.validateGetCapacityQuery(req.query).then((validationResp) => {
            // Validation complete - no need to log the full response
            // console.log(
            //     'validateGetCapacityInputQuery validationResp',
            //     validationResp
            // );
            if (validationResp.success) {
                return next();
            }

            res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                validationResp.resp
            );
        });
    } catch (error) {
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: 'failed',
            message: `${error.message}`,
            data: [],
        });
    }
};

router.get(
    '/new-req',
    validateGetCapacityQuery,
    validateGetCapacityInputQuery,
    (req, res, next) => {
        const model = require('../../../api_models/ace/capacity_model');
        setParamsToModel(req, model);

        model.getCapacity(req).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);

const setParamsToModel = (req, model) => {
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    return model.getFreshInstance(model);
};

module.exports = router;
