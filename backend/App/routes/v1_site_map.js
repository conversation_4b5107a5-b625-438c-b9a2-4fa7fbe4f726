var express = require('express');
var router = express.Router();
var { getUserContextFrmReq } = require('../api_models/utils/authrizor');

router.get('/overview_proto', function (req, res, next) {
    const site_map_model = setParamsToModel(req);
    site_map_model.getSiteMapOverviewProto(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/', function (req, res, next) {
    const site_map_model = setParamsToModel(req);
    site_map_model.getAllSites(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/export', function (req, res, next) {
    const site_map_model = setParamsToModel(req);
    site_map_model.exportSiteMapByEmail(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

const setParamsToModel = (req) => {
    const site_map_model =
        require('../api_models/site_map_model').getInstance();
    site_map_model.database = req.app.get('db');
    site_map_model.databaseReplica = req.app.get('db_replica');
    site_map_model.databaseDump = req.app.get('db_dump');
    site_map_model.ip_addr = req.ip;
    site_map_model.user_agent = req.get('User-Agent');
    site_map_model.user_context = getUserContextFrmReq(req);
    return site_map_model.getFreshInstance(site_map_model);
};

module.exports = router;
