const sampleOperationResp = require('./utils/operationResp');
const HttpStatus = require('http-status-codes');
const users_model = require('./users_model');
const pagination_filters_utils = require('./utils/pagination_filters_utils');
const db_resp = require('./utils/db_resp');

class site_map_model {
    getSiteMapOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }

            dbObj.tms_get_site_map_overview_proto(form_data, filters_).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_site_map_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    exportSiteMapByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            // TODO: Implement export functionality
            // const jobData = { requester, filters_ };
            // allQueues.WIFY_SITE_MAP_REQ_EXPORT_BY_EMAIL.addJob(jobData);

            resolve(
                new sampleOperationResp(
                    true,
                    'Export request submitted successfully',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    getAllSites(query) {
        return new Promise((resolve, reject) => {
            try {
                // Mock data for now - replace with actual database call later
                const mockSiteData = {
                    data: [
                        {
                            site_id: 'SITE001',
                            pincode: '110001',
                            geocoding_location_data: {
                                location: {
                                    lat: 28.6139,
                                    lng: 77.209,
                                },
                            },
                            site_name: 'Delhi Central Site',
                            address: 'Connaught Place, New Delhi',
                        },
                        {
                            site_id: 'SITE002',
                            pincode: '400001',
                            geocoding_location_data: {
                                location: {
                                    lat: 19.076,
                                    lng: 72.8777,
                                },
                            },
                            site_name: 'Mumbai Fort Site',
                            address: 'Fort, Mumbai',
                        },
                        {
                            site_id: 'SITE003',
                            pincode: '560001',
                            geocoding_location_data: {
                                location: {
                                    lat: 12.9716,
                                    lng: 77.5946,
                                },
                            },
                            site_name: 'Bangalore Central Site',
                            address: 'MG Road, Bangalore',
                        },
                        {
                            site_id: 'SITE004',
                            pincode: '600001',
                            geocoding_location_data: {
                                location: {
                                    lat: 13.0827,
                                    lng: 80.2707,
                                },
                            },
                            site_name: 'Chennai Central Site',
                            address: 'George Town, Chennai',
                        },
                        {
                            site_id: 'SITE005',
                            pincode: '700001',
                            geocoding_location_data: {
                                location: {
                                    lat: 22.5726,
                                    lng: 88.3639,
                                },
                            },
                            site_name: 'Kolkata Central Site',
                            address: 'BBD Bagh, Kolkata',
                        },
                        {
                            site_id: 'SITE006',
                            pincode: '500001',
                            geocoding_location_data: {
                                location: {
                                    lat: 17.385,
                                    lng: 78.4867,
                                },
                            },
                            site_name: 'Hyderabad Central Site',
                            address: 'Abids, Hyderabad',
                        },
                    ],
                    pagination: {
                        current: 1,
                        pageSize: 10,
                        total: 6,
                    },
                };

                // Apply search filter if provided
                if (query.search_query) {
                    const searchTerm = query.search_query.toLowerCase();
                    mockSiteData.data = mockSiteData.data.filter(
                        (site) =>
                            site.site_id.toLowerCase().includes(searchTerm) ||
                            site.pincode.includes(searchTerm) ||
                            site.site_name.toLowerCase().includes(searchTerm)
                    );
                    mockSiteData.pagination.total = mockSiteData.data.length;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(mockSiteData),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('SiteMapModel :: getAllSites :: error : ', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    // TODO: Implement actual database functions when ready
    // getAllSitesFromDB(query) {
    //     return new Promise((resolve, reject) => {
    //         var { pagination, filters_, search_query } =
    //             pagination_filters_utils.decodeQueryParams(query);
    //         var requesterInfo = {
    //             org_id: users_model.getOrgId(this.userContext),
    //             usr_id: users_model.getUUID(this.userContext),
    //             ip_address: this.ip_address,
    //             user_agent: this.user_agent_,
    //         };
    //         var form_data = JSON.stringify(requesterInfo);
    //         let dbObj = this.db;
    //
    //         dbObj
    //             .tms_get_site_map_data(requesterInfo, filters_, search_query)
    //             .then(
    //                 (res) => {
    //                     var dbResp = new db_resp(res[0].tms_get_site_map_data);
    //
    //                     if (!dbResp.status) {
    //                         resolve(
    //                             new sampleOperationResp(
    //                                 false,
    //                                 'Internal server Error',
    //                                 HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
    //                             )
    //                         );
    //                         return;
    //                     } else {
    //                         resolve(
    //                             new sampleOperationResp(
    //                                 true,
    //                                 JSON.stringify(dbResp.data),
    //                                 HttpStatus.StatusCodes.OK
    //                             )
    //                         );
    //                     }
    //                 },
    //                 (error) => {
    //                     this.fatalDbError(resolve, error);
    //                 }
    //             );
    //     });
    // }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }

    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set databaseDump(db) {
        this.dbDump = db;
    }

    get databaseDump() {
        return this.dbDump;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getFreshInstance(model) {
        const clonedInstance = new site_map_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new site_map_model();
        return instance;
    }
}

module.exports = new site_map_model();
