const sampleOperationResp = require('../utils/operationResp');
const db_resp = require('../utils/db_resp');
var HttpStatus = require('http-status-codes');
const users_model = require('../users_model');
const pagination_filters_utils = require('../utils/pagination_filters_utils');
const { callLambdaFn } = require('../utils/lambda_helpers');

class CAPACITY_API_Model {
    //This method is used to translate DB response to an API response (example is given below)
    //{"status":"success","message":"subtask created successfully","data":{"resp":[{"single_sbtsk_id":822,"single_input_data":{"sbtsk_ticket_id":"FIRS230428942191","ext_order_id":"08e3c2ce-9c46-4d55-9b4a-bb5292e4dcd1","sbtsk_type_id":3,"sbtsk_assignee":"65ef738a-5791-432d-9188-16bce946b3aa","sbtsk_priority":"Normal","sbtsk_start_day":"2023-05-08","sbtsk_start_time":"12:15PM","sbtsk_end_time":"12:30PM","sbtsk_remarks":"test","srvc_type_id":20}}]}}
    translateDbRespToApiResp(status, message, respData) {
        // Handle specific error codes
        if (message === 'capacity_module_disabled') {
            return {
                status: status,
                message: 'Capacity module is not enabled for this organization',
                data: respData,
            };
        }

        return {
            status: status,
            message: message,
            data: respData,
        };
    }

    getOrganisationsModel(query) {
        const Organisations_Model = require('../organisations_model');
        Organisations_Model.database = this.db;
        Organisations_Model.ip_addr = this.ip_address;
        Organisations_Model.user_agent = this.user_agent_;
        Organisations_Model.user_context = this.userContext;
        return Organisations_Model.getFreshInstance(Organisations_Model);
    }

    callLambdaFnForCapacitySkillData(query, aceCapacitySettingsData) {
        return new Promise(async (resolve, reject) => {
            // let config_data = await this.getConfigDataFrSrvcType({});
            // console.log('configData',configData);

            //Get lambda Arn Url
            const lambdaARN = aceCapacitySettingsData?.demand_translation_arn;

            if (lambdaARN == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
            }
            let payload = {
                is_api: true,
                org_id: query['org_id'],
                usr_id: query['usr_id'],
                ip_address: query['ip_address'],
                user_agent: query['user_agent'],
                filters: query['filters'],
                srvc_type_id: query['srvc_type_id'],
                translated_capacity_keys: query['translated_capacity_keys'],
                provider_id: query['provider_id'],
                pincode: query['pincode'],
                product_details: query['product_details'],
                vertical_id: query['vertical_id'],
                hub_id: query['hub_id'],
                // Include any other properties that might be needed
                // Fallback to original query for any properties not explicitly listed
                ...query,
            };
            console.log(
                'CAPACITY_API_Model :: callLambdaFnForCapacitySkillData :: STEP 1/2 - Preparing payload'
            );

            //Call lambda function here
            const params = {
                FunctionName: lambdaARN,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify(payload),
            };

            try {
                let respData = await callLambdaFn(params);
                let lambdaRespData = JSON.parse(respData.Payload);
                console.log(
                    'CAPACITY_API_Model :: callLambdaFnForCapacitySkillData :: STEP 2/2 - Response received'
                );
                if (lambdaRespData) {
                    resolve(
                        new sampleOperationResp(
                            lambdaRespData.status,
                            JSON.stringify(lambdaRespData),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Demand translation failed',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Could not process, please contact admin.',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
        });
    }

    // Method moved to capacity_update/capacity_read_model.js
    getCapacityFrResource(query) {
        console.log(
            'CAPACITY_API_Model :: getCapacityFrResource :: STEP 1/2 - Initializing'
        );

        const CapacityReadModel = require('../capacity_update/capacity_read_model');
        CapacityReadModel.database = this.db;
        CapacityReadModel.ip_addr = this.ip_address;
        CapacityReadModel.user_agent = this.user_agent_;
        CapacityReadModel.user_context = this.userContext;

        try {
            console.log(
                'CAPACITY_API_Model :: getCapacityFrResource :: STEP 2/2 - Calling read model'
            );
            return CapacityReadModel.getCapacityFrResource(query);
        } catch (error) {
            console.error(
                'CAPACITY_API_Model :: getCapacityFrResource :: ERROR:',
                error
            );
            throw error;
        }
    }

    // Alias for backward compatibility
    bookingService(query) {
        console.log(
            'CAPACITY_API_Model :: bookingService :: DEPRECATED - Use getCapacityFrResource'
        );
        return this.getCapacityFrResource(query);
    }

    getTranslatedCapacityKeys(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['filters'] = JSON.stringify(query.filters);
                query['srvc_type_id'] = query.srvc_type_id || 0;
                query['for_lambda'] = true;

                console.log(
                    'CAPACITY_API_Model :: getTranslatedCapacityKeys :: Processing request'
                );
                let translatedCapacityKeysResp = (
                    await this.db.tms_get_translated_capacity_keys(
                        JSON.stringify(query)
                    )
                )[0].tms_get_translated_capacity_keys;

                // console.log('translatedCapacityKeysResp',JSON.stringify(translatedCapacityKeysResp));
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(translatedCapacityKeysResp),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'CAPACITY_API_Model :: getTranslatedCapacityKeys',
                    error
                );
                this.fatalDbError(resolve, error);
            }
        });
    }

    /**
     * "Translate input to capacity keys
        <Provider_id><Vertical_id><Skill_id><Hub_id>
        Provider id if not given in input then, get from service type configuration(default service provider)
        Vertical_id, based on the service type id and brand id find the vertical id in above service provider
        Hub_id, based on the pincode and vertical find the matching hub id
        Skill_id, --- call configured lambda for demand translation
        Get capacity data from database operations"
     * @param {*} req
     * @returns
     */
    getCapacity(req) {
        return new Promise(async (resolve, reject) => {
            try {
                let query = req.query;
                // console.log('query', JSON.stringify(query));
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['filters'] = JSON.stringify(query.filters);
                query['srvc_type_id'] = query.srvc_type_id || 0;
                console.log(
                    'CAPACITY_API_Model :: getCapacity :: STEP 1/5 - Translating capacity keys'
                );
                const translatedCapacityKeysResp =
                    await this.getTranslatedCapacityKeys(query);
                // console.log(
                //     'translatedCapacityKeysResp',
                //     translatedCapacityKeysResp
                // );
                if (translatedCapacityKeysResp.success) {
                    const translatedCapacityKeys = JSON.parse(
                        translatedCapacityKeysResp?.resp
                    )?.data;
                    // console.log(
                    //     'translatedCapacityKeys',
                    //     translatedCapacityKeys
                    // );
                    query['translated_capacity_keys'] = translatedCapacityKeys;
                    query = { ...query, ...translatedCapacityKeys };
                }
                // console.log('getCapacity :: query', JSON.stringify(query));

                const OrganisationsModel = this.getOrganisationsModel(query);
                // console.log(
                //     'getCapacity :: OrganisationsModel',
                //     JSON.stringify(OrganisationsModel)
                // );

                const aceCapacitySettingsResp =
                    await OrganisationsModel.getAceCapacitySettingDataFrOrganisations(
                        query
                    );

                const aceCapacitySettingsData = JSON.parse(
                    aceCapacitySettingsResp.resp
                );

                // console.log(
                //     'getCapacity :: aceCapacitySettingsResp',
                //     aceCapacitySettingsResp
                // );

                const lambdaResponse =
                    await this.callLambdaFnForCapacitySkillData(
                        { ...query },
                        aceCapacitySettingsData
                    );

                if (!lambdaResponse.isSuccess()) {
                    resolve(lambdaResponse);
                    return;
                }
                const parsedLambdaResp = JSON.parse(lambdaResponse.resp);
                console.log(
                    'CAPACITY_API_Model :: getCapacity :: STEP 3/5 - Lambda response parsed'
                );
                const lambdasData = parsedLambdaResp.data;
                query = { ...query, ...lambdasData };

                const bookingServicesResp =
                    await this.getCapacityFrResource(query);

                console.log(
                    'CAPACITY_API_Model :: getCapacity :: STEP 4/5 - Capacity data retrieved'
                );

                // Check if the capacity retrieval was successful
                if (!bookingServicesResp.isSuccess()) {
                    console.log(
                        'CAPACITY_API_Model :: getCapacity :: Capacity retrieval failed'
                    );
                    resolve(bookingServicesResp);
                    return;
                }

                // Check if the response is already an object or a JSON string
                let bookingServicesData;
                if (
                    bookingServicesResp?.resp &&
                    typeof bookingServicesResp.resp === 'object'
                ) {
                    bookingServicesData = bookingServicesResp.resp.data;
                } else if (
                    bookingServicesResp?.resp &&
                    typeof bookingServicesResp.resp === 'string'
                ) {
                    bookingServicesData = JSON.parse(
                        bookingServicesResp.resp
                    )?.data;
                } else {
                    bookingServicesData = [];
                }
                console.log(
                    'CAPACITY_API_Model :: getCapacity :: STEP 5/5 - Data processed'
                );
                // console.log('srvcReqs',JSON.stringify(srvcReqs));
                resolve(
                    new sampleOperationResp(
                        true,
                        {
                            status: 'success',
                            message: 'Capacity found successfully',
                            data: bookingServicesData,
                        },
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('CAPACITY_API_Model :: getCapacity', error);
                this.fatalDbError(resolve, error);
            }
        });
    }

    validateGetCapacityQuery(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['org_timezone'] = users_model.getOrgTimezone(
                    this.userContext
                );

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                let form_data = JSON.stringify(query);
                // console.log("form_data",form_data);
                let resp =
                    await this.db.tms_validate_get_capacity_query(form_data);

                var dbResp = new db_resp(
                    resp[0].tms_validate_get_capacity_query
                );

                // console.log("dbResp",dbResp);

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            this.translateDbRespToApiResp(
                                'failed',
                                dbResp.code,
                                dbResp.data
                            ),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        this.translateDbRespToApiResp(
                            'success',
                            dbResp.code,
                            dbResp.data
                        ),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(error);
                this.fatalDbError(resolve, error, true);
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getFreshInstance(model) {
        const clonedInstance = new CAPACITY_API_Model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new CAPACITY_API_Model();
