const sampleOperationResp = require('../utils/operationResp');
const db_resp = require('../utils/db_resp');
var HttpStatus = require('http-status-codes');
const users_model = require('../users_model');
const pagination_filters_utils = require('../utils/pagination_filters_utils');
const { allQueues } = require('../queues_v2/queues');
const {
    dumpExportReqCounter,
    dumpExportsCounter,
    dumpExportSuccessCounter,
    dumpExportFailureCounter,
} = require('../utils/metrics');
const { moduleKeys } = require('../utils/helper');
const { dumpExportStatus } = require('../utils/metrics');
const fs = require('fs');
const path = require('path');
const JSONStream = require('JSONStream');
const jsonToCsv = require('json-to-csv-stream');

class availability_report_model {
    constructor() {
        this.db = null;
        this.dbReplica = null;
        this.dbDump = null;
        this.ip_address = null;
        this.user_agent_ = null;
        this.userContext = null;
    }

    getAvailabilityOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);

            // Create requester object
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            dbObj
                .tms_get_user_availability_report_overview_proto(
                    requester,
                    filters_
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_user_availability_report_overview_proto
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getAllAvailability(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;

        return new Promise((resolve, reject) => {
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            dbObj
                .tms_get_user_availability_report(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_user_availability_report
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    exportAvailabilityByEmail(query) {
        return new Promise(async (resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['userContext'] = this.userContext;

            const jobData = { requester, filters_ };

            allQueues.WIFY_USER_AVAILABILITY_EXPORT_BY_EMAIL.addJob(jobData);

            dumpExportReqCounter.inc({
                module: moduleKeys.availabilityReport,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processAvailabilityExportByEmail(jobData) {
        //console.log('processAvailabilityExportByEmail', jobData);
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;

                const dbObj = this.databaseDump || this.db;
                if (!dbObj) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                console.log('filters_', filters_);

                dbObj
                    .tms_get_user_availability_report_dump(
                        requesterInfo,
                        filters_,
                        {
                            stream: true,
                        }
                    )
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today, now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'user_availability_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `User Availability dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.availability,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_user_availability_report_dump',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                                // stream.pipe(JSONStream.stringify()).pipe(jsonToCsv()).pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.availability,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                console.log(
                    'attendance_model :: processAvailabilityExportByEmail :: error :: ',
                    error
                );
                dumpExportFailureCounter.inc({
                    module: moduleKeys.availabilityReport,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                resolve(
                    new sampleOperationResp(
                        false,
                        'Internal server Error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }
    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set databaseDump(db) {
        this.dbDump = db;
    }

    get databaseDump() {
        return this.dbDump;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new availability_report_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new availability_report_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new availability_report_model();
