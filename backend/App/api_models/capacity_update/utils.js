/**
 * Utility functions for capacity update operations
 */

/**
 * Transforms a capacity record from the database format to the API format
 * @param {Object} record - The capacity record from the database
 * @param {string} orgId - The organization ID
 * @param {string} orgName - The organization name
 * @returns {Object} - The transformed capacity record
 */
function transformCapacityRecord(record, orgId, orgName) {
    // Extract the necessary fields from the record
    // Adjust the field names based on your actual database schema
    return {
        resourceId: record.resource_id || '',
        startTime: record.start_time || '',
        endTime: record.end_time || '',
        totalCapacity: parseInt(record.total_capacity || 0, 10),
        availableCapacity: parseInt(record.available_capacity || 0, 10),
        bookedCapacity: parseInt(record.booked_capacity || 0, 10),
        organizationId: orgId,
        organizationName: orgName,
        metadata: {
            verticalId: record.vertical_id || '',
            verticalName: record.vertical_name || '',
            skillId: record.skill_id || '',
            skillName: record.skill_name || '',
            hubId: record.hub_id || '',
            hubName: record.hub_name || '',
            hubCode: record.hub_code || '',
            providerId: record.provider_id || '',
        },
    };
}

module.exports = {
    transformCapacityRecord,
};
