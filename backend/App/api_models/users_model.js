const http_utils = require('./utils/http_utils');
const jwt = require('jsonwebtoken');
const {
    GET_LOGIN_IN_CACHE,
    NEED_TO_UPDATE_CACHE,
} = require('./pub_sub_event_handlers');
const { GET_ATTENDANCE_IN_CACHE } = require('./utils/redis_helpers');
const moment = require('moment');
const db_resp = require('./utils/db_resp');
const sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');

const HUMAN_THRESHOLD = 10; // seconds

class users_model {
    // helper functions to decode user_data
    // uuid : payload.uuid,
    // user_data : JSON.parse(payload.user_data),
    // hash : payload.hash,
    // identity : payload.identity,
    // identity_type : payload.identity_type

    isServiceProvider(userContext) {
        return userContext?.user_details?.org.org_type == 'ORG_TYPE_SRVC_PRVDR';
    }
    getOrgId(userContext) {
        // console.log("User context - ",userContext.user_details);
        return userContext?.user_details?.org.id;
    }

    getUserName(userContext) {
        // console.log('User context - ', userContext.user_details);
        return userContext?.user_details?.name;
    }

    getUUID(userContext) {
        // console.log("User context - ",userContext);
        return userContext.uuid;
    }
    getHash(userContext) {
        // console.log("User context - ",userContext.uuid);
        return userContext.hash;
    }
    getUserRoles(userContext) {
        return this.getUserDetailsAsInFrontend(userContext).roles;
    }
    getUserDetailsAsInFrontend(userContext) {
        // console.log("User context - ",userContext.uuid);
        return userContext.user_details;
    }

    isTokenActive(token) {
        return new Promise((resolve, reject) => {
            let hasExpired = true;
            let payload;
            try {
                payload = jwt.verify(token, process.env.JWT_SECRET);
            } catch (error) {
                // we dont care about the error
                return resolve(!hasExpired);
            }
            if (payload) {
                // console.log("Payload in token",payload);
                if (payload.exp) {
                    // console.log('Expiry exists')
                    const expiry = payload.exp;
                    const now = Math.floor(Date.now() / 1000);
                    // console.log("Token expiry - ",expiry)
                    // console.log("Now - ",now)
                    hasExpired = now > expiry;
                    // console.log("Human threshold secs -> ", HUMAN_THRESHOLD);
                    if (!hasExpired && now - payload.ts < HUMAN_THRESHOLD) {
                        // console.log("Creation time less than ", HUMAN_THRESHOLD);
                        return resolve(!hasExpired);
                    }
                    // if(now -  <= HUMAN_THRESHOLD){
                    //     return resolve(!hasExpired);
                    // }
                } else {
                    hasExpired = false;
                }
            }
            // console.log("process.env.machineHash",process.env.machineHash);

            if (!hasExpired) {
                var uuid = payload.uuid;
                var hash = payload.hash;
                console.log('trying to verify hash from redis..');
                GET_LOGIN_IN_CACHE(uuid, hash).then((value) => {
                    if (value != 'cache_failed') {
                        hasExpired = value > 0 ? false : true;
                    }
                    console.log('verify hash resp -> ', value);
                    resolve(!hasExpired);
                });
            } else {
                resolve(!hasExpired);
            }
        });
    }

    getRequesterInfo(req) {
        return new Promise((resolve, reject) => {
            // console.log("Getting requester info");
            const token = http_utils.getAuthTokenFromRequest(req);
            // console.log("Token from request",token);
            if (token) {
                // console.log("Token rxd, checking if active...");
                this.isTokenActive(token).then(async (isValid) => {
                    var userData;
                    let need_to_update_cache = false;
                    if (isValid) {
                        let payload;
                        try {
                            payload = jwt.verify(token, process.env.JWT_SECRET);
                            if (
                                process.env.CHECK_APP_CACHE_UPDATE === '1' &&
                                req.method === 'GET' &&
                                !req.url?.includes('verify-user-token')
                            ) {
                                let uuid = payload.uuid;
                                let hash = payload.hash;
                                need_to_update_cache =
                                    await NEED_TO_UPDATE_CACHE(uuid, hash);
                            }
                            userData = {
                                uuid: payload.uuid,
                                user_data: JSON.parse(payload.user_data),
                                hash: payload.hash,
                                identity: payload.identity,
                                identity_type: payload.identity_type,
                                is_check_technician_app:
                                    payload.is_check_technician_app,
                            };
                        } catch (error) {
                            // we dont care about the error
                            isValid = false;
                        }
                    }
                    const output = {
                        token: token,
                        isValid: isValid,
                        ...userData,
                        need_to_update_cache,
                    };
                    resolve(output);
                });
            } else {
                resolve(null);
            }
        });
    }

    async isUserPunchIn(user_id) {
        let day = moment().format('YYYY-MM-DD');
        const attendanceResult = await GET_ATTENDANCE_IN_CACHE(user_id, day);
        return attendanceResult?.start_time;
    }

    getUserLogins(user_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_user_logins(user_id).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_get_user_logins);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getFreshInstance(model) {
        const clonedInstance = new users_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new users_model();
        return instance;
    }

    getOrgTimezone(userContext) {
        // console.log("User context - ",userContext);
        return userContext?.user_details?.timezone;
    }
}

module.exports = new users_model();
