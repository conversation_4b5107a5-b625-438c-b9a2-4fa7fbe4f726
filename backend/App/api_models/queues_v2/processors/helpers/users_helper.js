const setParamsToUserModel = (user_model, dataObj, mainDb, replicaDb) => {
    user_model.db = mainDb;
    user_model.databaseReplica = replicaDb;
    user_model.ip_addr = dataObj.ip_addr;
    user_model.user_agent = dataObj.user_agent;
    user_model.user_context = dataObj.user_context;
};

exports.getUsersModelFrQueue = (app, user_model_data) => {
    const user_model = require('../../../users_model').getInstance();
    setParamsToUserModel(
        user_model,
        user_model_data,
        app.get('db'),
        app.get('db_replica')
    );
    return user_model.getFreshInstance(user_model);
};
