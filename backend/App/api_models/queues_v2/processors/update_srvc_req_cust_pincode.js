const {
    getDataTransformModelFrQueue,
} = require('./helpers/data_transform_helper');

/**
 * Queue processor for updating cust_pincode column in service requests
 * Extracts pincode from form_data JSON and updates the dedicated column
 */
const performJob = async (job, done) => {
    try {
        const app = require('../../../app');

        const {
            serviceTypeId,
            batchSize,
            batchNumber,
            totalBatches,
            data_transform_model_data,
        } = job.data;

        console.log(
            `ServiceType::${serviceTypeId}::Processing batch ${batchNumber}/${totalBatches}`
        );

        // Get the data transform model from queue
        const dataTransformModel = getDataTransformModelFrQueue(
            app,
            data_transform_model_data
        );

        // Call the model method to update the batch
        const updateResult =
            await dataTransformModel.updateSrvcReqCustPincodeBatch(
                serviceTypeId,
                batchSize
            );

        if (!updateResult.isSuccess()) {
            console.error(
                `ServiceType::${serviceTypeId}::Batch ${batchNumber} failed:`,
                updateResult.resp
            );
            throw new Error(updateResult.resp || 'Failed to update batch');
        }

        const resultData = JSON.parse(updateResult.resp);
        const updatedCount = resultData.updated_count || 0;

        console.log(
            `ServiceType::${serviceTypeId}::Updated ${updatedCount} records in batch ${batchNumber}/${totalBatches}`
        );

        done(null, {
            serviceTypeId,
            batchNumber,
            totalBatches,
            updatedCount,
            success: true,
        });
    } catch (error) {
        console.error(`Error updating cust_pincode: ${error.message}`, error);

        // Determine if we should retry based on the error type
        const isRetryable =
            !error.message.includes('constraint violation') &&
            !error.message.includes('syntax error');

        // Get attempt information from the job
        const attemptsMade = job.attemptsMade || 0;
        const maxAttempts = job.opts.attempts || 10;

        if (isRetryable && attemptsMade < maxAttempts - 1) {
            // If retryable and we haven't exceeded max attempts, fail the job to trigger retry
            console.log(
                `Batch ${job.data.batchNumber} | RETRY: scheduled | NEXT: ${attemptsMade + 2}/${maxAttempts}`
            );
            done(new Error(`Retryable error: ${error.message}`));
        } else {
            // If not retryable or we've exceeded max attempts, mark as complete but with error
            console.error(
                `Batch ${job.data.batchNumber} | FAILED: permanently | ATTEMPTS: ${attemptsMade + 1}`
            );
            done(null, {
                batchNumber: job.data.batchNumber,
                serviceTypeId: job.data.serviceTypeId,
                success: false,
                message: `Service request cust_pincode update failed after ${attemptsMade + 1} attempts: ${error.message || 'Unknown error'}`,
                error: error.message,
                finalAttempt: true,
            });
        }
    }
};

exports.default = performJob;
