CREATE OR REPLACE FUNCTION public.tms_get_user_availability_aggregated_report_dump(requester json, filters_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;

	availability_data json;
	total_count integer;
	offset_val integer;
    slots json;
	org_timezone text;
    -- usr_capacity_details json;

    org_availability_slots_config json;
    filter_days text[];
    filter_from_date timestamp;
    filter_to_date timestamp;
    filter_available text[];
   	current_date_ timestamp;
    --filter_available text[];
    filter_locations text[];
    filter_technician_roles int[];
    filter_service_hubs int[];
    data_ json;
    pagination json; 
begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = requester->>'usr_id';
	ip_address_ = requester->>'ip_address';  
	user_agent_ = requester->>'user_agent';
	org_id_ = (requester->>'org_id')::integer;

	-- Calculate offset
	

    org_timezone = tms_hlpr_get_org_timezone(org_id_);
    current_date_ = (now() at time zone 'utc');
    
    filter_days = array(select json_array_elements_text(json_extract_path(filters_,'days')))::text[];
	if cardinality(filter_days) > 0 then
	 	filter_from_date =  filter_days[1]::timestamp at time zone 'utc' at time zone 'Asia/kolkata';
		filter_to_date   =  filter_days[2]::timestamp at time zone 'utc' at time zone 'Asia/kolkata';
	else
		filter_from_date =  current_date_;
		filter_to_date   =  current_date_;
	end if;

    filter_available = array(select json_array_elements_text(json_extract_path(filters_,'availability')))::text[];
   --  filter_available = array(select json_array_elements_text(json_extract_path(filters_,'availability')))::text[];
    filter_locations = array(select json_array_elements_text(json_extract_path(filters_,'locations')))::text[];
    filter_technician_roles = array(select json_array_elements_text(json_extract_path(filters_,'technician_roles')))::int[];
    filter_service_hubs = array(select json_array_elements_text(json_extract_path(filters_,'service_hubs')))::int[];

    select  org_settings.settings_data
      from  public.cl_tx_orgs_settings as org_settings
     where  org_settings.org_id = org_id_
       and  org_settings.settings_type = 'ACE_AVAILABILITY_SLOTS_CONFIG'
      into  org_availability_slots_config;

     if org_availability_slots_config is null then
        status = false;
        message = 'capacity_details_missing';
        resp_data = jsonb_build_object(
            'status', status,
            'code', message
        );
        return resp_data;
        --raise exception 'capacity_details_missing';
     end if;


     WITH all_days AS (
		    SELECT 
		        user_.usr_id,
		        user_.name AS technician_name,
		        srvc_hub.hub_name  AS service_hub_name,
		        day_::date AS day,
		        COUNT(*) FILTER (WHERE usr_availability.is_present IS TRUE) AS present_slots_count,
		        (SELECT COUNT(*) FROM json_array_elements(org_availability_slots_config->'generated_slots')) AS total_slots
		      FROM cl_tx_users user_
		     INNER JOIN generate_series(filter_from_date, filter_to_date, '1 day'::interval) AS day_ 
		        ON true
		     INNER JOIN json_array_elements(org_availability_slots_config->'generated_slots') AS slot_ 
		        ON true
		      LEFT JOIN cl_tx_usr_availability usr_availability 
		        ON usr_availability.user_id = user_.usr_id
		       AND usr_availability.usr_tmzone_day = date(day_)
		       AND usr_availability.is_active IS TRUE
		       AND tms_hlpr_match_usr_availability(usr_availability, slot_, org_timezone, usr_tmzone_day) IS TRUE
		     INNER JOIN cl_tx_usr_roles usr_role 
		        ON usr_role.user_id = user_.usr_id
		       AND (
		   			cardinality(filter_technician_roles) = 0 
		   		 	or usr_role.role_id = any(filter_technician_roles)
				   )
			 inner join cl_tx_vertical_srvc_hubs as srvc_hub 
			    on srvc_hub.id = user_.primary_srvc_hub 	   
		     WHERE user_.primary_srvc_hub IS NOT NULL
		       AND user_.org_id = org_id_
		       AND user_.is_active IS true
		       AND (
				    cardinality(filter_locations) = 0 
				    or exists (
				        select 1
				          from unnest(filter_locations::int[]) as filter_locations(location_id)
       				     where location_id = any(user_.loc_group::int[]) 
				     )
				   )
			   AND (
			         cardinality(filter_service_hubs) = 0 
			         or user_.primary_srvc_hub = any(filter_service_hubs)
			       )

		     GROUP BY day_, user_.usr_id, user_."name", srvc_hub.id 
		),
		availability_status AS (
		    SELECT 
		        technician_name,
		        service_hub_name,
		        day,
		        CASE 
		            WHEN present_slots_count = total_slots THEN 'Fully Present'
		            WHEN present_slots_count > 0 THEN 'Limited'
		            ELSE 'Offline'
		        END AS availability
		    FROM all_days
		)
		SELECT json_agg(
		    json_build_object(
		        'Technician Name', technician_name,
		        'Service Hub', service_hub_name,
		        'Day', day,
		        'Availability', availability
		    )
		) AS availability_report
		FROM availability_status
		into resp_data;
   

	return resp_data;

end;
$function$
;
