CREATE OR REPLACE FUNCTION public.tms_get_site_map_overview_proto(form_data json, filter_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
	status boolean;
 	message text;
 	org_id_ integer;
 	user_id_ uuid;
 
 --temp
 	resp_data json;
 	filter_proto json default '{}';
 	verticals_list_ json;
 	_config_data json;
 	filter_possbile_orgs json;
 	filter_location_groups json;
 	updated_filter_location_groups json;
 	_state_list jsonb;
 	_site_creation_date_range json;

 -- Filters
    filter_brands int[];
    filter_vertical_id int;

	--Output
    current_date_ timestamp;
   

begin
		
	status = false;
	message = 'Internal_error';
	
	--form data 
	org_id_ = json_extract_path_text(form_data,'org_id');
    user_id_ = form_data->>'usr_id';
	current_date_ = (now() at time zone 'utc');
	
    -- Handle vertical filter safely
    if json_array_length(filter_->'verticals_list') > 0 then
        filter_vertical_id = (array( select json_array_elements_text(filter_->'verticals_list'))::text[])[1]::int;
    else
        filter_vertical_id = 0;
    end if;

  --customer filter
	filter_brands = array(select json_array_elements_text(filter_->'brands'))::int[];
    if cardinality(filter_brands) = 0 then
    	filter_brands = tms_get_assgnd_org_to_user(user_id_);
	end if;


    _config_data = '{}';
	if filter_vertical_id > 0 then
		_config_data = jsonb_set(_config_data::jsonb,'{vertical_form_data}',
			(tms_get_sp_custom_fields_details(form_data,filter_vertical_id)->'data'->'form_data')::jsonb,true);
	end if;

 --verticals list for SP
    verticals_list_ = tms_hlpr_get_verticals_list(org_id_);
 
    filter_possbile_orgs = tms_get_customer_orgs_with_count(org_id_,user_id_,form_data,filter_)->'data';

	filter_location_groups = tms_get_loc_grps_fr_select(form_data)->'data';
	updated_filter_location_groups = '{"label": "Empty", "value": -2}'::jsonb || filter_location_groups::jsonb ;

	-- Get state list for site location filtering
	_state_list = array_to_json(array(
		select jsonb_build_object(
			   	'value',state,
				'label',state
			   )
	      from sys_cf_loc_mstr
		 where country_code = 'IN'
		 group by state 
		 order by state asc
    ))::jsonb;

--by default
	status = true;
	message = 'success';

    filter_proto = json_build_object('locations',updated_filter_location_groups::json);
    filter_proto = jsonb_set(filter_proto::jsonb,'{brands}',to_jsonb(filter_possbile_orgs),true);
    filter_proto = jsonb_set(filter_proto::jsonb,'{verticals_list}',verticals_list_::jsonb,true);
    filter_proto = jsonb_set(filter_proto::jsonb,'{config_data}',_config_data::jsonb,true);
    filter_proto = jsonb_set(filter_proto::jsonb,'{state_list}',_state_list::jsonb,true);
	resp_data = json_build_object('filters_proto',filter_proto); 

   
	return json_build_object('status',status,'code',message,'data',resp_data);
   

end;
$function$
;
