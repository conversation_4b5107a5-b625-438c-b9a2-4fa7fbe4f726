CREATE OR REPLACE FUNCTION public.tms_get_user_availability_report_overview_proto(form_data json, filters_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
	--filters_ json;

	total_capacity integer;
	available_capacity integer;
	utilized_capacity integer;
	utilization_percentage numeric;
    role_data json;
    filter_proto json;
   
   -- Overview counts
	total_users integer;
	present_users integer;
	limited_users integer;
	offline_users integer;
   	-- Filter variables
	filter_days text[];
	filter_from_date timestamp;
	filter_to_date timestamp;
	filter_locations json;
	filter_technician_roles int[];
	filter_service_hubs int[];
	current_date_ timestamp;
	org_timezone text;
	org_availability_slots_config json;
    service_hubs json;
    dashboard_count json;
    filter_location_grp text[];

begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';  
	user_agent_ = form_data->>'user_agent';
	org_id_ = (form_data->>'org_id')::integer;


    org_timezone = tms_hlpr_get_org_timezone(org_id_);
    current_date_ = (now() at time zone 'utc'); 
    -- Parse filters
	filter_days = array(select json_array_elements_text(json_extract_path(filters_,'days')))::text[];
	if cardinality(filter_days) > 0 then
		filter_from_date = filter_days[1]::timestamp at time zone 'utc' at time zone 'Asia/kolkata';
		filter_to_date = filter_days[2]::timestamp at time zone 'utc' at time zone 'Asia/kolkata';
	else
		filter_from_date = current_date_;
		filter_to_date = current_date_;
	end if;

    filter_location_grp = array(select json_array_elements_text(json_extract_path(filters_,'locations')))::text[];
    filter_technician_roles = array(select json_array_elements_text(json_extract_path(filters_,'technician_roles')))::int[];
    filter_service_hubs = array(select json_array_elements_text(json_extract_path(filters_,'service_hubs')))::int[];

	-- Calculate capacity metrics

   	-- Get organization availability slots configuration
	select org_settings.settings_data
	from public.cl_tx_orgs_settings as org_settings
	where org_settings.org_id = org_id_
	and org_settings.settings_type = 'ACE_AVAILABILITY_SLOTS_CONFIG'
	into org_availability_slots_config;

    if org_availability_slots_config is null then
		status = false;
		message = 'capacity_details_missing';
		resp_data = jsonb_build_object(
			'status', status,
			'code', message
		);
		return resp_data;
	end if;


   WITH all_days AS (
		    SELECT  user_.usr_id,		    
			        day_::date AS day,
			        COUNT(*) FILTER (WHERE usr_availability.is_present IS TRUE) AS present_slots_count,
			        (SELECT COUNT(*) FROM json_array_elements(org_availability_slots_config->'generated_slots')) AS total_slots
		      FROM cl_tx_users user_
		     INNER JOIN generate_series(filter_from_date, filter_to_date, '1 day'::interval) AS day_ 
		        ON true
		     INNER JOIN json_array_elements(org_availability_slots_config->'generated_slots') AS slot_ 
		        ON true
		      LEFT JOIN cl_tx_usr_availability usr_availability 
		        ON usr_availability.user_id = user_.usr_id
		       AND usr_availability.usr_tmzone_day = date(day_)
		       AND usr_availability.is_active IS TRUE
		       AND tms_hlpr_match_usr_availability(usr_availability, slot_, org_timezone, usr_tmzone_day) IS TRUE
		     INNER JOIN cl_tx_usr_roles usr_role 
		        ON usr_role.user_id = user_.usr_id
		       AND (
		   			cardinality(filter_technician_roles) = 0 
		   		 	or usr_role.role_id = any(filter_technician_roles)
				   )
		     WHERE user_.primary_srvc_hub IS NOT NULL
		       AND user_.org_id = org_id_
		       AND user_.is_active IS true
		       AND (
				    cardinality(filter_location_grp) = 0 
				    or exists (
				        select 1
				          from unnest(filter_location_grp::int[]) as filter_location_grp(location_id)
       				     where location_id = any(user_.loc_group::int[]) 
				     )
				   )
			   AND (
			         cardinality(filter_service_hubs) = 0 
			         or user_.primary_srvc_hub = any(filter_service_hubs)
			       )
		     GROUP BY user_.usr_id, user_.name, day_
		),
		aggregated_counts AS (
		    SELECT
		      -- technician_name,
		        COUNT(*) FILTER (WHERE present_slots_count = total_slots) AS fully_present_day_count,
		        COUNT(*) FILTER (WHERE present_slots_count > 0 AND present_slots_count < total_slots) AS limited_present_day_count,
		        COUNT(*) FILTER (WHERE present_slots_count = 0) AS offline_day_count
		    FROM all_days
		  
		)
		SELECT jsonb_build_object(
		    'fully_present', fully_present_day_count,
		    'limited', limited_present_day_count,
		    'offline', offline_day_count,
		    'total_user', (fully_present_day_count + limited_present_day_count + offline_day_count)
		)
		 FROM aggregated_counts
		 into dashboard_count;

	
	
    role_data= array_to_json(array(
	   select jsonb_build_object(
					'value', (roles.role_id::text) ,
					'label', (roles.name_details).title ,
					'title', (roles.name_details).title
			   )
		 from public.cl_cf_roles as roles
		where roles.org_id = org_id_
		group by roles.role_id
		order by (roles.name_details).title
	));
  
    service_hubs = array_to_json(array(
	   select jsonb_build_object(
					'value', hubs.id  ,
					'label', hubs.hub_name  ,
					'title', hubs.hub_name 
			   )
		 from cl_tx_vertical_srvc_hubs as hubs
		where hubs.org_id = org_id_
		order by hubs.hub_name asc
	));

    filter_locations = tms_get_loc_grps_fr_select(form_data)->'data';
    
    filter_proto = json_build_object(
    					'technician_roles', role_data, 
						'service_hubs',service_hubs,
						'locations',filter_locations
   				   );
		
	status = true;
	message = 'success';
	resp_data := jsonb_build_object(
		'status', status,
		'code', message,
		'data', jsonb_build_object(			
			'filters_proto',filter_proto,
			'dashboardData',dashboard_count
			
		)
	);

	return resp_data;

end;
$function$
;
